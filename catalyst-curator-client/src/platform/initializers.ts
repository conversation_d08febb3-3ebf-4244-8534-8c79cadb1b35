import { action, makeObservable, observable } from 'mobx';
import { ErrorInfo } from '../lib';
import { StoresProvider } from '../lib/stores/StoresProvider';
import urqlClient, { initializeClient } from '../services/urql/CuratorUrqlClient';
import { Assets } from '@react-navigation/elements';
import { Asset } from 'expo-asset';
import {
  ApplicationStore,
  CategoryStore,
  OpportunityListStore,
  OpportunityReportsStore,
  StakeholderStore,
  TenantStore,
  UserStore,
} from '../stores';
import { OrganizationStore } from '../stores/OrganizationStore';
import { Store } from '../lib/stores/Store';
import { UsersAdminStore } from '../stores/UsersAdminStore';
import { PlatformFactory } from './PlatformFactory';
import { en, registerTranslation } from 'react-native-paper-dates';
import ProjectsStore from '../stores/ProjectsStore';
import { ResourcesStore } from '../stores/ResourcesStore';
import { OpportunityOwnerStore } from '../stores/OpportunityOwnerStore';

registerTranslation('en', en);

export type CuratorStores = {
  ApplicationStore: ApplicationStore;
  TenantStore: TenantStore;
  OpportunityListStore: OpportunityListStore;
  UserStore: UserStore;
  UsersAdminStore: UsersAdminStore;
  CategoryStore: CategoryStore;
  StakeholderStore: StakeholderStore;
  OrganizationStore: OrganizationStore;
  ProjectsStore: ProjectsStore;
  ResourcesStore: ResourcesStore;
  OpportunityReportsStore: OpportunityReportsStore;
  OpportunityOwnerStore: OpportunityOwnerStore;
};

export class PlatformInitializer {
  initializationError?: Error;
  private static instance: PlatformInitializer;

  static initialize(): PlatformInitializer {
    if (this.instance) return this.instance;
    this.instance = new PlatformInitializer();
    this.instance.initialize();
    return this.instance;
  }

  private constructor(public appInitialized = false) {
    makeObservable<PlatformInitializer, 'initialize'>(this, { appInitialized: observable, initialize: action });
  }

  private async initialize(): Promise<void> {
    try {
      await PlatformFactory.getPlatformInitializer()();
    } catch (e) {
      console.log(e);
      this.initializationError = e as Error;
    } finally {
      this.appInitialized = true;
    }
  }
}

export async function initialize(tenantHandle: string, graphQLUrl: string, origin?: string): Promise<void> {
  Asset.loadAsync(Assets);
  // Note: Order matters here
  initializeClient(graphQLUrl);
  await initializeStores(tenantHandle, graphQLUrl, origin);
  // load cached data
  const storesProvider = StoresProvider.get<CuratorStores>();
  await storesProvider.restoreStores();
  // assign logout handler to newly created user store
  urqlClient.onLogout = () => storesProvider.getStore('UserStore').signOut();
}

async function initializeStores(tenantHandle: string, graphQLUrl: string, origin?: string): Promise<void> {
  const storesProvider = StoresProvider.get<CuratorStores>();
  // Note: Order matters here, if one store depends on another

  // these initialize asynchrounsly but can be intialized concurrently
  const applicationStore = new ApplicationStore({ graphqlUrl: graphQLUrl, origin: origin });
  storesProvider.setStore('ApplicationStore', applicationStore);
  storesProvider.setStore('UserStore', new UserStore());

  const tenantStore = new TenantStore(tenantHandle);

  await Promise.all([applicationStore.loadApplication(), tenantStore.loadTenant()]);
  storesProvider.setStore('TenantStore', tenantStore);

  // these initialize synchronously
  const userStore = storesProvider.getStore('UserStore');
  storesProvider.setStore('ResourcesStore', new ResourcesStore(userStore));
  const resourcesStore = storesProvider.getStore('ResourcesStore');
  storesProvider.setStore('OpportunityListStore', new OpportunityListStore(resourcesStore));
  storesProvider.setStore('OpportunityReportsStore', new OpportunityReportsStore(resourcesStore));
  storesProvider.setStore('UsersAdminStore', new UsersAdminStore());
  storesProvider.setStore('CategoryStore', new CategoryStore());
  storesProvider.setStore('StakeholderStore', new StakeholderStore());
  storesProvider.setStore('ProjectsStore', new ProjectsStore());
  storesProvider.setStore('OpportunityOwnerStore', new OpportunityOwnerStore());

  Store.setSystemErrorHandler((errorInfo: ErrorInfo, display = true) => {
    if (errorInfo.nextMessage && display) {
      applicationStore.systemMessage = { message: errorInfo.nextMessage };
    }
  });
}
