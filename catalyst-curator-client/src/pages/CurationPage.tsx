import { observable } from 'mobx';
import { observer } from 'mobx-react';
import { Component } from 'react';
import { ScrollView, StyleProp, StyleSheet, TextStyle, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunity } from './sections/opportunity/CuratedOpportunity';
import { OpportunityHeader } from './sections/opportunity/OpportunityHeader';
import { SubmittedOpportunity } from './sections/opportunity/SubmittedOpportunity';
import { Router } from '../platform/Router';
import { MainStackParamList } from '../routing/screens';
import { CategoryStore } from '../stores/CategoryStore';
import OpportunityStore from '../stores/OpportunityStore';
import { StakeholderStore } from '../stores/StakeholderStore';
import UserStore from '../stores/UserStore';
import { TenantStore } from '../stores';
import { OpportunityOwnerStore } from '../stores/OpportunityOwnerStore';
import { OpportunityOwnerListStore } from '../stores/OpportunityOwnerListStore';

const _styles: Record<string, StyleProp<ViewStyle> | StyleProp<TextStyle>> = StyleSheet.create({
  content: {
    flexGrow: 1,
    marginBottom: 25,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
  },
  section: {
    flexGrow: 1,
    flexShrink: 1,
    marginBottom: 15,
  },
  sectionTitle: {
    marginBottom: 5,
  },
  sectionLabel: {
    fontWeight: 'bold',
    marginBottom: 3,
  },
  sectionValue: {
    marginBottom: 25,
  },
});

const sectionStyle = {
  section: _styles.section,
  sectionTitle: _styles.sectionTitle,
  sectionLabel: _styles.sectionLabel,
  sectionValue: _styles.sectionValue,
};

export interface SectionStyle {
  section: StyleProp<ViewStyle>;
  sectionTitle: StyleProp<TextStyle>;
  sectionLabel: StyleProp<TextStyle>;
  sectionValue: StyleProp<TextStyle>;
}

interface CurationPageProps {
  tenantStore: TenantStore;
  opportunityStore: OpportunityStore;
  router: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  categoryStore: CategoryStore;
  stakeholderStore: StakeholderStore;
  opportunityOwnersStore: OpportunityOwnerStore;
  opportunityOwnerListStore: OpportunityOwnerListStore;
}

interface Locals {
  showSubmission: boolean;
}

@observer
class CurationPage extends Component<CurationPageProps> {
  private locals: Locals = observable({ showSubmission: false });

  render() {
    const {
      opportunityStore,
      categoryStore,
      stakeholderStore,
      tenantStore,
      router,
      theme,
      userStore,
      opportunityOwnersStore,
      opportunityOwnerListStore,
    } = this.props;
    const { colors, fonts } = theme;
    const { margins, paddings, components } = theme.styles;
    const defaultButtonStyle = { backgroundColor: colors.buttonSecondary, borderWidth: 0 };
    const defaultButtonLabelStyle = { color: colors.textSecondary };
    const activeButtonColors = {
      ...defaultButtonStyle,
      backgroundColor: colors.buttonPrimary,
      borderColor: colors.border,
    };
    const activeButtonLabelStyles = { ...defaultButtonLabelStyle, color: colors.brandFontColor };

    return (
      <ScrollView>
        <View style={[components.flexAll, margins.BottomL]}>
          <OpportunityHeader
            opportunityOwnerListStore={opportunityOwnerListStore}
            opportunityStore={opportunityStore}
            router={router}
            onProjectCreated={this.handleProjectCreated}
            onDownload={() => handleDownload(opportunityStore)}
          />
          <View style={[components.pageContainerConstraints]}>
            <View style={[components.flexAll, components.globalPageConstraints]}>
              <View style={[_styles.content]}>
                <View style={[{ backgroundColor: colors.background }, paddings.TopL]}>
                  {this.locals.showSubmission && (
                    <SubmittedOpportunity
                      opportunityStore={opportunityStore}
                      sectionStyle={sectionStyle}
                      style={sectionStyle.section}
                      onCuratedToggle={this.handleOnCuratedVersionPress}
                    />
                  )}
                  {!this.locals.showSubmission && (
                    <CuratedOpportunity
                      opportunityOwnerListStore={opportunityOwnerListStore}
                      userStore={userStore}
                      tenantStore={tenantStore}
                      opportunityStore={opportunityStore}
                      categoryStore={categoryStore}
                      stakeholderStore={stakeholderStore}
                      opportunityOwnersStore={opportunityOwnersStore}
                      onProjectCreated={this.handleProjectCreated}
                      router={router}
                      onSubmissionToggle={this.handleOnSubmissionPress}
                    />
                  )}
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    );
  }

  handleProjectCreated = (id: string) => {
    this.props.router.navigate('project', { id });
  };

  handleOnCuratedVersionPress = () => {
    this.locals.showSubmission = false;
  };

  handleOnSubmissionPress = () => {
    this.locals.showSubmission = true;
  };

  handleOnUpdateOpportunity = (): void => {};
}

const handleDownload = async (opportunityStore: OpportunityStore) => {
  const year = new Date().getFullYear();
  const month = new Date().getMonth() + 1;
  const day = new Date().getDate();
  const fileName: string = `${[year, month, day].join('-')}-opportunity.xlsx`;
  opportunityStore.handleDownload(fileName);
};

export default withTheme(CurationPage);
