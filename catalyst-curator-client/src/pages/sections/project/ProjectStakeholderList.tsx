import { View } from 'react-native';
import { StakeholderForm } from '../opportunity/curated-opportunity-groups/StakeholderForm';
import { ProjectStakeholder, ProjectStakeholderType, UpdateOperator } from '../../../services/codegen/types';
import { StakeholderStore } from '../../../stores';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Button } from '../../../lib';
import { ProjectStore } from '../../../stores/ProjectStore';

type ProjectStakeholderListProps = {
  stakeholder: ProjectStakeholder;
  stakeholderStore: StakeholderStore;
  theme: ReactNativePaper.ThemeProp;
  projectStore: ProjectStore;
  onStakeholderChanged: (
    projectStore: ProjectStore,
    operator: UpdateOperator,
    id: string,
    type: ProjectStakeholderType,
  ) => Promise<void>;
};
export const ProjectStakeholderList = withTheme(
  observer(
    ({ stakeholder, stakeholderStore, onStakeholderChanged, projectStore, theme }: ProjectStakeholderListProps) => {
      const { colors, fonts } = theme;
      const isStakeholderEditable = (stakeholderId: string) => {
        return locals.editingStakeholderId === stakeholderId;
      };

      const locals = useLocalObservable(() => ({
        editingStakeholderId: null as string | null,
        isAddingNew: false,
        setEditingStakeholderId(id: string | null) {
          this.editingStakeholderId = id;
        },
        setIsAddingNew(value: boolean) {
          this.isAddingNew = value;
          if (!value) {
            stakeholderStore.clearBuffer();
          }
        },
      }));

      async function updateStakeholder() {
        if (!stakeholderStore.validateAll()) {
          return;
        }

        await stakeholderStore.updateStakeholder(stakeholder.stakeholder.id);
        await onStakeholderChanged(projectStore, UpdateOperator.Set, stakeholder.stakeholder.id, stakeholder.type);
      }

      async function deleteStakeholder() {
        await onStakeholderChanged(projectStore, UpdateOperator.Rm, stakeholder.stakeholder.id, stakeholder.type);
      }
      return (
        <View key={stakeholder.id} style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
          <View style={{ flexDirection: 'row', gap: 8, alignItems: 'flex-start' }}>
            <View style={{ flex: 1 }}>
              <StakeholderForm
                stakeholder={stakeholder.stakeholder}
                stakeholderStore={stakeholderStore}
                isNew={false}
                editable={isStakeholderEditable(stakeholder.id)}
                theme={theme}
              />
              {!isStakeholderEditable(stakeholder.id) && (
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 8,
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    marginTop: 16,
                  }}
                >
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => {
                      locals.setEditingStakeholderId(stakeholder.id);
                      stakeholderStore.initializeBufferForEditing(stakeholder.stakeholder);
                    }}
                  >
                    Edit
                  </Button>
                </View>
              )}
              {isStakeholderEditable(stakeholder.id) && (
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 8,
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: 16,
                  }}
                >
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.error }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => deleteStakeholder()}
                    iconName="delete"
                  >
                    Delete
                  </Button>
                  <View style={{ gap: 8, flexDirection: 'row' }}>
                    <Button
                      labelStyle={[
                        { textTransform: 'capitalize', color: colors.secondaryTextColor },
                        fonts.mediumTitle,
                      ]}
                      type="secondary"
                      compact
                      onPress={() => locals.setEditingStakeholderId(null)}
                    >
                      Cancel
                    </Button>
                    <Button
                      labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                      type="primary"
                      getDisabled={() => !stakeholderStore.isFormValid()}
                      compact
                      onPress={async () => {
                        await updateStakeholder();
                        locals.setEditingStakeholderId(null);
                      }}
                    >
                      Save
                    </Button>
                  </View>
                </View>
              )}
            </View>
          </View>
        </View>
      );
    },
  ),
);
