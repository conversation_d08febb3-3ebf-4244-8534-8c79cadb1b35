import { observer } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { StakeholderStore } from '../../../stores';
import { ProjectStore } from '../../../stores/ProjectStore';
import { Label } from '../../../lib';
import { ProjectStakeholderType, UpdateOperator } from '../../../services/codegen/types';
import { OpportunityStakeholdersHelp } from './OpportunityStakeholdersHelp';
import { ProjectStakeholderList } from './ProjectStakeholderList';

interface ProjectStakeholdersProps {
  projectStore: ProjectStore;
  stakeholderStore: StakeholderStore;
  theme: ReactNativePaper.ThemeProp;
  onStakeholderChanged: (
    projectStore: ProjectStore,
    operator: UpdateOperator,
    id: string,
    type: ProjectStakeholderType,
  ) => Promise<void>;
}
export const ProjectStakeholders = withTheme(
  observer(({ projectStore, stakeholderStore, theme, onStakeholderChanged }: ProjectStakeholdersProps) => {
    const {
      styles: { components, margins, fontSizes },
    } = theme;

    const divisionStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Division,
    );

    const performerStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Performer,
    );

    const transitionStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Transition,
    );
    return (
      <View style={[components.rowStyle, { width: '75%', zIndex: 2 }]}>
        <OpportunityStakeholdersHelp
          getOpportunityStakeholders={() => projectStore.project?.opportunities?.[0]?.stakeholders}
        />
        <View style={{ gap: 16 }}>
          <View>
            <Label>Division Stakeholders</Label>
            {divisionStakeholders?.map((stakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={stakeholder.id}
                stakeholder={stakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
          </View>
          <View>
            <Label>Performer Stakeholders</Label>
            {performerStakeholders?.map((stakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={stakeholder.id}
                stakeholder={stakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
          </View>
          <View>
            <Label>Transition Stakeholders</Label>
            {transitionStakeholders?.map((stakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={stakeholder.id}
                stakeholder={stakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
          </View>
        </View>
      </View>
    );
  }),
);
