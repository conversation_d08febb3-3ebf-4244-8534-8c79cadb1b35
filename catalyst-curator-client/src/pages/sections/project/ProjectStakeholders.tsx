import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { StakeholderStore } from '../../../stores';
import { ProjectStore } from '../../../stores/ProjectStore';
import { Label, Button } from '../../../lib';
import { ProjectStakeholderType, UpdateOperator } from '../../../services/codegen/types';
import { OpportunityStakeholdersHelp } from './OpportunityStakeholdersHelp';
import { ProjectStakeholderList } from './ProjectStakeholderList';
import { StakeholderForm } from '../opportunity/curated-opportunity-groups/StakeholderForm';

interface ProjectStakeholdersProps {
  projectStore: ProjectStore;
  stakeholderStore: StakeholderStore;
  theme: ReactNativePaper.ThemeProp;
  onStakeholderChanged: (
    projectStore: ProjectStore,
    operator: UpdateOperator,
    id: string,
    type: ProjectStakeholderType,
  ) => Promise<void>;
}
export const ProjectStakeholders = withTheme(
  observer(({ projectStore, stakeholderStore, theme, onStakeholderChanged }: ProjectStakeholdersProps) => {
    const {
      styles: { components },
      colors,
      fonts,
    } = theme;

    const locals = useLocalObservable(() => ({
      addingDivision: false,
      addingPerformer: false,
      addingTransition: false,

      setAddingDivision(value: boolean) {
        this.addingDivision = value;
        if (!value) {
          stakeholderStore.clearBuffer();
        }
      },

      setAddingPerformer(value: boolean) {
        this.addingPerformer = value;
        if (!value) {
          stakeholderStore.clearBuffer();
        }
      },

      setAddingTransition(value: boolean) {
        this.addingTransition = value;
        if (!value) {
          stakeholderStore.clearBuffer();
        }
      },
    }));

    const divisionStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Division,
    );

    const performerStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Performer,
    );

    const transitionStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Transition,
    );

    async function addStakeholder(type: ProjectStakeholderType) {
      if (!stakeholderStore.validateAll()) {
        return;
      }

      const newStakeholder = await stakeholderStore.addStakeholder();
      await onStakeholderChanged(projectStore, UpdateOperator.Add, newStakeholder.id, type);

      locals.setAddingDivision(false);
      locals.setAddingPerformer(false);
      locals.setAddingTransition(false);
    }
    return (
      <View style={[components.rowStyle, { width: '75%', zIndex: 2 }]}>
        <OpportunityStakeholdersHelp
          getOpportunityStakeholders={() => projectStore.project?.opportunities?.[0]?.stakeholders}
        />
        <View style={{ gap: 16 }}>
          <View>
            <Label>Division Stakeholders</Label>

            {!locals.addingDivision && (
              <Button
                labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                type="secondary"
                compact
                onPress={() => locals.setAddingDivision(true)}
                iconName="plus-box-outline"
                iconRight
              >
                Add Division Stakeholder
              </Button>
            )}

            {locals.addingDivision && (
              <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8, marginBottom: 16 }}>
                <StakeholderForm isNew={true} editable={true} stakeholderStore={stakeholderStore} theme={theme} />
                <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => locals.setAddingDivision(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                    type="primary"
                    compact
                    onPress={async () => await addStakeholder(ProjectStakeholderType.Division)}
                    getDisabled={() => !stakeholderStore.isFormValid()}
                  >
                    Add
                  </Button>
                </View>
              </View>
            )}

            {divisionStakeholders?.map((stakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={stakeholder.id}
                stakeholder={stakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
          </View>
          <View>
            <Label>Performer Stakeholders</Label>

            {!locals.addingPerformer && (
              <Button
                labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                type="secondary"
                compact
                onPress={() => locals.setAddingPerformer(true)}
                iconName="plus-box-outline"
                iconRight
              >
                Add Performer Stakeholder
              </Button>
            )}

            {locals.addingPerformer && (
              <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8, marginBottom: 16 }}>
                <StakeholderForm isNew={true} editable={true} stakeholderStore={stakeholderStore} theme={theme} />
                <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => locals.setAddingPerformer(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                    type="primary"
                    compact
                    onPress={async () => await addStakeholder(ProjectStakeholderType.Performer)}
                    getDisabled={() => !stakeholderStore.isFormValid()}
                  >
                    Add
                  </Button>
                </View>
              </View>
            )}

            {performerStakeholders?.map((stakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={stakeholder.id}
                stakeholder={stakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
          </View>
          <View>
            <Label>Transition Stakeholders</Label>

            {!locals.addingTransition && (
              <Button
                labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                type="secondary"
                compact
                onPress={() => locals.setAddingTransition(true)}
                iconName="plus-box-outline"
                iconRight
              >
                Add Transition Stakeholder
              </Button>
            )}

            {locals.addingTransition && (
              <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8, marginBottom: 16 }}>
                <StakeholderForm isNew={true} editable={true} stakeholderStore={stakeholderStore} theme={theme} />
                <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => locals.setAddingTransition(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                    type="primary"
                    compact
                    onPress={async () => await addStakeholder(ProjectStakeholderType.Transition)}
                    getDisabled={() => !stakeholderStore.isFormValid()}
                  >
                    Add
                  </Button>
                </View>
              </View>
            )}

            {transitionStakeholders?.map((stakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={stakeholder.id}
                stakeholder={stakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
          </View>
        </View>
      </View>
    );
  }),
);
