import { observer } from 'mobx-react';
import React, { Component } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Debouncer, LabeledInput } from '../../../lib';
import { Column, FieldRow, InputSet } from '../../../lib/ui/molecules/InputSet';
import {
  ProjectStakeholder,
  ProjectStakeholderType,
  Stakeholder,
  UpdateOperator,
} from '../../../services/codegen/types';
import {  StakeholderStore } from '../../../stores/StakeholderStore';
import { OpportunityStakeholdersHelp } from './OpportunityStakeholdersHelp';

interface ProjectStakeholderProps {
  style?: StyleProp<ViewStyle>;
  getStakeholderStore: () => StakeholderStore;
  getProjectStakeholders: () => ProjectStakeholder[] | undefined;
  onStakeholderChanged: (
    operator: UpdateOperator,
    stakeholderId: string,
    type: ProjectStakeholderType,
  ) => Promise<void>;
  onStakeholderPriorityUp: (stakeholderId: string, type: ProjectStakeholderType) => void;
  onStakeholderPriorityDown: (stakeholderId: string, type: ProjectStakeholderType) => void;
  getOpportunityStakeholders: () => Stakeholder[] | undefined;
  theme: ReactNativePaper.ThemeProp;
}

@observer
class ProjectStakeholders extends Component<ProjectStakeholderProps> {
  private debouncer = Debouncer();
  render() {
    const {
      getStakeholderStore,
      getProjectStakeholders,
      onStakeholderPriorityUp,
      onStakeholderPriorityDown,
      getOpportunityStakeholders,
      style,
      theme,
    } = this.props;
    const divisionNameSearchStore = getStakeholderStore().getStakeholderSearchStore('division_name');
    const divisionOrgSearchStore = getStakeholderStore().getStakeholderSearchStore('division_org');
    const performerNameSearchStore = getStakeholderStore().getStakeholderSearchStore('performer_name');
    const performerOrgSearchStore = getStakeholderStore().getStakeholderSearchStore('performer_org');
    const transitionNameSearchStore = getStakeholderStore().getStakeholderSearchStore('transition_name');
    const transitionOrgSearchStore = getStakeholderStore().getStakeholderSearchStore('transition_org');
    const {
      styles: { margins, paddings },
      fontSizes,
      colors,
    } = theme;
    return (
      <View style={[{ zIndex: 2 }, style]}>
        <OpportunityStakeholdersHelp {...{ getOpportunityStakeholders }} />
        <LabeledInput
          style={[{ zIndex: 3 }, margins.TopL]}
          labelText={'Division Stakeholders'}
          labelProps={{ textStyle: [fontSizes.mediumSmall] }}
          key={'division'}
        >
          <InputSet
            style={{}}
            getColumns={this.getColumns}
            getRows={() => this.getRows(ProjectStakeholderType.Division, getProjectStakeholders)}
            rowLabel="Stakeholder"
            getSearchValue={(column) =>
              this.handleGetSearchValue(divisionNameSearchStore, divisionOrgSearchStore, column) || ''
            }
            setSearchValue={(column, value) =>
              this.handleSetSearchValue(divisionNameSearchStore, divisionOrgSearchStore, column, value)
            }
            getSearchResults={(column) =>
              this.getSearchResults(column, divisionNameSearchStore, divisionOrgSearchStore)
            }
            addText="Add Division Stakeholder"
            onSubmitNewValues={() =>
              this.handleOnSubmitStakeholder(
                divisionNameSearchStore,
                divisionOrgSearchStore,
                ProjectStakeholderType.Division,
              )
            }
            onRemoveRow={(row) => this.handleOnRemoveStakeholder(row, ProjectStakeholderType.Division)}
            onMoveRowUp={(row) => onStakeholderPriorityUp(row.id, ProjectStakeholderType.Division)}
            onMoveRowDown={(row) => onStakeholderPriorityDown(row.id, ProjectStakeholderType.Division)}
            allowReorder={true}
          />
        </LabeledInput>
        <LabeledInput
          style={[{ zIndex: 2 }, margins.TopM]}
          labelText={'Performer Stakeholders'}
          labelProps={{ textStyle: [fontSizes.mediumSmall] }}
          key={'performer'}
        >
          <InputSet
            style={{}}
            getColumns={this.getColumns}
            getRows={() => this.getRows(ProjectStakeholderType.Performer, getProjectStakeholders)}
            rowLabel="Stakeholder"
            getSearchValue={(column) =>
              this.handleGetSearchValue(performerNameSearchStore, performerOrgSearchStore, column) || ''
            }
            setSearchValue={(column, value) =>
              this.handleSetSearchValue(performerNameSearchStore, performerOrgSearchStore, column, value)
            }
            getSearchResults={(column) =>
              this.getSearchResults(column, performerNameSearchStore, performerOrgSearchStore)
            }
            addText="Add Performer Stakeholder"
            onSubmitNewValues={() =>
              this.handleOnSubmitStakeholder(
                performerNameSearchStore,
                performerOrgSearchStore,
                ProjectStakeholderType.Performer,
              )
            }
            onRemoveRow={(row) => this.handleOnRemoveStakeholder(row, ProjectStakeholderType.Performer)}
            onMoveRowUp={(row) => onStakeholderPriorityUp(row.id, ProjectStakeholderType.Performer)}
            onMoveRowDown={(row) => onStakeholderPriorityDown(row.id, ProjectStakeholderType.Performer)}
            allowReorder={true}
          />
        </LabeledInput>
        <LabeledInput
          style={[{ zIndex: 1 }, margins.TopM]}
          labelText={'Transition Stakeholders'}
          labelProps={{ textStyle: [fontSizes.mediumSmall] }}
          key={'transition'}
        >
          <InputSet
            style={{}}
            getColumns={this.getColumns}
            getRows={() => this.getRows(ProjectStakeholderType.Transition, getProjectStakeholders)}
            rowLabel="Stakeholder"
            getSearchValue={(column) =>
              this.handleGetSearchValue(transitionNameSearchStore, transitionOrgSearchStore, column) || ''
            }
            setSearchValue={(column, value) =>
              this.handleSetSearchValue(transitionNameSearchStore, transitionOrgSearchStore, column, value)
            }
            getSearchResults={(column) =>
              this.getSearchResults(column, transitionNameSearchStore, transitionOrgSearchStore)
            }
            addText="Add Transition Stakeholder"
            onSubmitNewValues={() =>
              this.handleOnSubmitStakeholder(
                transitionNameSearchStore,
                transitionOrgSearchStore,
                ProjectStakeholderType.Transition,
              )
            }
            onRemoveRow={(row) => this.handleOnRemoveStakeholder(row, ProjectStakeholderType.Transition)}
            onMoveRowUp={(row) => onStakeholderPriorityUp(row.id, ProjectStakeholderType.Transition)}
            onMoveRowDown={(row) => onStakeholderPriorityDown(row.id, ProjectStakeholderType.Transition)}
            allowReorder={true}
          />
        </LabeledInput>
      </View>
    );
  }

  componentWillUnmount() {
    const { getStakeholderStore } = this.props;
    getStakeholderStore().clearAll();
  }

  getColumns = () => [
    { fieldName: 'name', label: 'Contact Name' },
    { fieldName: 'org', label: 'Organization' },
  ];

  getRows = (type: ProjectStakeholderType, getProjectStakeholders: () => ProjectStakeholder[] | undefined) => {
    return (
      getProjectStakeholders()
        ?.filter((projectStakeholder) => projectStakeholder.type === type)
        .map((projectStakeholder) => ({
          id: projectStakeholder.stakeholder.id,
          fieldValues: [projectStakeholder.stakeholder.name || '', projectStakeholder.stakeholder.org || ''],
        })) || []
    );
  };

  getSearchResults = (
    column: Column,
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
  ) => {
    let values;
    if (column.fieldName === 'name') values = nameSearchStore.uniqueMatches('name');
    else if (column.fieldName === 'org') values = orgSearchStore.uniqueMatches('org');
    return (
      values?.map((value) => ({
        label: value,
        value,
      })) || []
    );
  };

  handleOnRemoveStakeholder = (row: FieldRow, type: ProjectStakeholderType) => {
    const stakeholderId = row.id;
    const { onStakeholderChanged } = this.props;
    onStakeholderChanged(UpdateOperator.Rm, stakeholderId, type);
  };

  handleOnPress = (text: string) => {
    //this.handleOnSearchValueChange(text);
  };

  handleOnSubmitStakeholder = async (
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
    type: ProjectStakeholderType,
  ) => {
    const { getStakeholderStore, onStakeholderChanged } = this.props;
    const stakeholderStore = getStakeholderStore();
    try {
      const stakeholder = await stakeholderStore.addStakeholder({
        name: nameSearchStore.getSearchValue('name'),
        org: orgSearchStore.getSearchValue('org'),
      });
      if (onStakeholderChanged) await onStakeholderChanged(UpdateOperator.Add, stakeholder.id, type);
      nameSearchStore.clearAll();
      orgSearchStore.clearAll();
    } catch (error) {
      stakeholderStore.addError(error as any, false);
    }
  };

  handleGetSearchValue = (
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
    column: Column,
  ) => {
    if (column.fieldName === 'name') return nameSearchStore.getSearchValue('name');
    if (column.fieldName === 'org') return orgSearchStore.getSearchValue('org');
  };

  handleSetSearchValue = (
    nameSearchStore: StakeholderSearchStore,
    orgSearchStore: StakeholderSearchStore,
    column: Column,
    value: any,
  ) => {
    if (column.fieldName === 'name') nameSearchStore.setFieldSearchValue({ fieldName: 'name', value });
    else if (column.fieldName === 'org') orgSearchStore.setFieldSearchValue({ fieldName: 'org', value });
    this.debouncer(() => {
      if (column.fieldName === 'name') nameSearchStore.queryStakeholders();
      else if (column.fieldName === 'org') orgSearchStore.queryStakeholders();
    }, 500);
    //.clearErrorMessage();
  };
}

export default withTheme(ProjectStakeholders);
