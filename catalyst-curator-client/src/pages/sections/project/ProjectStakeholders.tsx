import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { StakeholderStore } from '../../../stores';
import { ProjectStore } from '../../../stores/ProjectStore';
import { Button, Hr, Label } from '../../../lib';
import { ProjectStakeholderType, UpdateOperator } from '../../../services/codegen/types';
import { ProjectStakeholderList } from './ProjectStakeholderList';
import { StakeholderForm } from '../opportunity/curated-opportunity-groups/StakeholderForm';

interface ProjectStakeholdersProps {
  projectStore: ProjectStore;
  stakeholderStore: StakeholderStore;
  theme: ReactNativePaper.ThemeProp;
  onStakeholderChanged: (
    projectStore: ProjectStore,
    operator: UpdateOperator,
    id: string,
    type: ProjectStakeholderType,
  ) => Promise<void>;
}
export const ProjectStakeholders = withTheme(
  observer(({ projectStore, stakeholderStore, theme, onStakeholderChanged }: ProjectStakeholdersProps) => {
    const {
      styles: { components },
      colors,
      fonts,
      fontSizes,
    } = theme;

    const locals = useLocalObservable(() => ({
      isAddingStakeholder: false,
      setIsAddingStakeholder(value: boolean) {
        this.isAddingStakeholder = value;
      },
    }));
    const divisionStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Division,
    );

    const performerStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Performer,
    );

    const transitionStakeholders = projectStore.project?.projectStakeholders.filter(
      (stakeholder) => stakeholder.type === ProjectStakeholderType.Transition,
    );

    async function addStakeholder() {
      if (stakeholderStore.hasErrors) {
        return;
      }

      const type = stakeholderStore.getValue('type') as ProjectStakeholderType;
      const newStakeholder = await stakeholderStore.addStakeholder();
      await onStakeholderChanged(projectStore, UpdateOperator.Add, newStakeholder.id, type);
      locals.setIsAddingStakeholder(false);
    }

    return (
      <View style={[components.rowStyle, { width: '75%', zIndex: 2 }]}>
        <View style={{ gap: 16 }}>
          <View style={{ gap: 16 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Label>Uncategorized Stakeholders</Label>
              {!locals.isAddingStakeholder && (
                <Button
                  labelStyle={[
                    { color: colors.secondaryTextColor, textTransform: 'capitalize' },
                    fonts.mediumTitle,
                    fontSizes.xSmall,
                  ]}
                  style={{ height: 24 }}
                  type="secondary"
                  onPress={() => {
                    stakeholderStore.clearBuffer();
                    stakeholderStore.clearErrors();
                    locals.setIsAddingStakeholder(true);
                    stakeholderStore.setValue('type', ProjectStakeholderType.Division);
                  }}
                >
                  Add Stakeholder
                </Button>
              )}
            </View>
            {locals.isAddingStakeholder && (
              <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8, marginBottom: 16 }}>
                <StakeholderForm
                  isNew={true}
                  isEditing={true}
                  stakeholderStore={stakeholderStore}
                  theme={theme}
                  isProjectStakeholder
                  defaultType={ProjectStakeholderType.Division}
                  // TODO: change type to Uncatagorized when it is made. also display uncatagorized stakeholders
                />
                <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => locals.setIsAddingStakeholder(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                    type="primary"
                    compact
                    onPress={async () => await addStakeholder()}
                    getDisabled={() => stakeholderStore.hasErrors}
                  >
                    Add
                  </Button>
                </View>
              </View>
            )}
            <Hr />
            <Label>Division Stakeholders</Label>
            {divisionStakeholders?.map((projectStakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={projectStakeholder.id}
                projectStakeholder={projectStakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
            {divisionStakeholders?.length === 0 && (
              <Label textStyle={{ color: colors.secondaryTextColor }}>No Division Stakeholders</Label>
            )}
          </View>
          <Hr />
          <View style={{ gap: 16 }}>
            <Label>Performer Stakeholders</Label>
            {performerStakeholders?.map((projectStakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={projectStakeholder.id}
                projectStakeholder={projectStakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
            {performerStakeholders?.length === 0 && (
              <Label textStyle={{ color: colors.secondaryTextColor }}>No Performer Stakeholders</Label>
            )}
          </View>
          <Hr />
          <View style={{ gap: 16 }}>
            <Label>Transition Stakeholders</Label>
            {transitionStakeholders?.map((projectStakeholder) => (
              <ProjectStakeholderList
                onStakeholderChanged={onStakeholderChanged}
                projectStore={projectStore}
                key={projectStakeholder.id}
                projectStakeholder={projectStakeholder}
                stakeholderStore={stakeholderStore}
                theme={theme}
              />
            ))}
            {transitionStakeholders?.length === 0 && (
              <Label textStyle={{ color: colors.secondaryTextColor }}>No Transition Stakeholders</Label>
            )}
          </View>
        </View>
      </View>
    );
  }),
);
