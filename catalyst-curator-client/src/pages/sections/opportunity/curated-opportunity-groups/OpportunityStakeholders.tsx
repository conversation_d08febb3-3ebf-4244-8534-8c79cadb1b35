import { View } from 'react-native';
import { Button } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Group } from '../../../../lib/ui/atoms/Group';
import { StakeholderForm, OpportunityStakeholder } from './StakeholderForm';
import { StakeholderStore } from '../../../../stores';
import { UpdateStakeholderInput } from '../../../../services/codegen/types';

interface OpportunityStakeholderProps {
  editable: boolean;
  label: string;
  theme: ReactNativePaper.ThemeProp;
  stakeholderStore: StakeholderStore;
}

export const OpportunityStakeholders = withTheme(
  observer(({ editable, label, theme, stakeholderStore }: OpportunityStakeholderProps) => {
    const { colors, fonts } = theme;

    const locals = useLocalObservable(() => ({
      editingStakeholderId: null as string | null,
      isAddingNew: false,
      newStakeholder: {
        firstName: '',
        lastName: '',
        title: '',
        phone: '',
        email: '',
        altEmail: '',
        organization: '',
        organizationRole: '',
      } as Omit<OpportunityStakeholder, 'id'>,

      setEditingStakeholderId(id: string | null) {
        this.editingStakeholderId = id;
      },

      setIsAddingNew(value: boolean) {
        this.isAddingNew = value;
        if (!value) {
          this.resetNewStakeholder();
        }
      },

      resetNewStakeholder() {
        this.newStakeholder = {
          firstName: '',
          lastName: '',
          title: '',
          phone: '',
          email: '',
          altEmail: '',
          organization: '',
          organizationRole: '',
        };
      },

      updateNewStakeholder(field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) {
        this.newStakeholder[field] = value;
      },

      addStakeholder() {
        stakeholderStore.addStakeholder({
          name: this.newStakeholder.firstName + ' ' + this.newStakeholder.lastName,
          org: this.newStakeholder.organization,
        });
        this.setIsAddingNew(false);
      },

      deleteStakeholder(id: string) {
        stakeholderStore.deleteStakeholder(id);
      },

      updateStakeholder(id: string, input: UpdateStakeholderInput) {
        stakeholderStore.updateStakeholder(input);
      },
    }));

    const isStakeholderEditable = (stakeholderId: string) => {
      return editable && locals.editingStakeholderId === stakeholderId;
    };

    return (
      <Group title="Potential Stakeholders" style={{ zIndex: 2 }} description={label}>
        <View style={{ gap: 16 }}>
          {editable && !locals.isAddingNew && (
            <Button
              labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
              type="secondary"
              compact
              onPress={() => locals.setIsAddingNew(true)}
              iconName="plus-box-outline"
              iconRight
            >
              Add Stakeholder
            </Button>
          )}

          {locals.isAddingNew && (
            <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
              <StakeholderForm
                stakeholder={null}
                isNew={true}
                editable={true}
                getValue={(field: keyof Omit<OpportunityStakeholder, 'id'>) => locals.newStakeholder[field] || ''}
                setValue={(field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) =>
                  locals.updateNewStakeholder(field, value)
                }
                theme={theme}
              />
              <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                <Button
                  labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                  type="secondary"
                  compact
                  onPress={() => locals.setIsAddingNew(false)}
                >
                  Cancel
                </Button>
                <Button
                  labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                  type="primary"
                  compact
                  onPress={() => locals.addStakeholder()}
                >
                  Add
                </Button>
              </View>
            </View>
          )}
          {locals.stakeholders.map((stakeholder: OpportunityStakeholder) => (
            <View key={stakeholder.id} style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
              <View style={{ flexDirection: 'row', gap: 8, alignItems: 'flex-start' }}>
                <View style={{ flex: 1 }}>
                  <StakeholderForm
                    stakeholder={stakeholder}
                    isNew={false}
                    editable={isStakeholderEditable(stakeholder.id)}
                    getValue={(field: keyof Omit<OpportunityStakeholder, 'id'>) => stakeholder[field] || ''}
                    setValue={(field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) =>
                      locals.updateStakeholder(stakeholder.id, field, value)
                    }
                    theme={theme}
                  />
                  {editable && !isStakeholderEditable(stakeholder.id) && (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 8,
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        marginTop: 16,
                      }}
                    >
                      <Button
                        labelStyle={[
                          { textTransform: 'capitalize', color: colors.secondaryTextColor },
                          fonts.mediumTitle,
                        ]}
                        type="secondary"
                        compact
                        onPress={() => locals.setEditingStakeholderId(stakeholder.id)}
                      >
                        Edit
                      </Button>
                    </View>
                  )}
                  {editable && isStakeholderEditable(stakeholder.id) && (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 8,
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginTop: 16,
                      }}
                    >
                      <Button
                        labelStyle={[{ textTransform: 'capitalize', color: colors.error }, fonts.mediumTitle]}
                        type="secondary"
                        compact
                        onPress={() => locals.deleteStakeholder(stakeholder.id)}
                        iconName="delete"
                      >
                        Delete
                      </Button>
                      <View style={{ gap: 8, flexDirection: 'row' }}>
                        <Button
                          labelStyle={[
                            { textTransform: 'capitalize', color: colors.secondaryTextColor },
                            fonts.mediumTitle,
                          ]}
                          type="secondary"
                          compact
                          onPress={() => locals.setEditingStakeholderId(null)}
                        >
                          Cancel
                        </Button>
                        <Button
                          labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                          type="primary"
                          compact
                          onPress={() => locals.setEditingStakeholderId(null)}
                        >
                          Save
                        </Button>
                      </View>
                    </View>
                  )}
                </View>
              </View>
            </View>
          ))}
        </View>
      </Group>
    );
  }),
);
