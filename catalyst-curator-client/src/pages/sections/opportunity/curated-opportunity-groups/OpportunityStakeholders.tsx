import { View } from 'react-native';
import { Button } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Group } from '../../../../lib/ui/atoms/Group';
import { StakeholderForm, OpportunityStakeholder } from './StakeholderForm';
import { StakeholderStore } from '../../../../stores';
import { StakeholderListStore } from '../../../../stores/StakeholderListStore';
import { UpdateStakeholderInput } from '../../../../services/codegen/types';

interface OpportunityStakeholderProps {
  editable: boolean;
  label: string;
  theme: ReactNativePaper.ThemeProp;
  stakeholderStore: StakeholderStore;
  stakeholderListStore?: StakeholderListStore;
}

export const OpportunityStakeholders = withTheme(
  observer(({ editable, label, theme, stakeholderStore, stakeholderListStore }: OpportunityStakeholderProps) => {
    const { colors, fonts } = theme;

    const locals = useLocalObservable(() => ({
      editingStakeholderId: null as string | null,
      isAddingNew: false,
      newStakeholder: {
        firstName: '',
        lastName: '',
        title: '',
        phone: '',
        email: '',
        altEmail: '',
        organization: '',
        organizationRole: '',
      } as Omit<OpportunityStakeholder, 'id'>,

      setEditingStakeholderId(id: string | null) {
        this.editingStakeholderId = id;
      },

      setIsAddingNew(value: boolean) {
        this.isAddingNew = value;
        if (!value) {
          this.resetNewStakeholder();
        }
      },

      resetNewStakeholder() {
        this.newStakeholder = {
          firstName: '',
          lastName: '',
          title: '',
          phone: '',
          email: '',
          altEmail: '',
          organization: '',
          organizationRole: '',
        };
      },

      updateNewStakeholder(field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) {
        this.newStakeholder[field] = value;
      },

      async addStakeholder() {
        try {
          // Set values in the stakeholder store
          stakeholderStore.setValue('firstName', this.newStakeholder.firstName);
          stakeholderStore.setValue('lastName', this.newStakeholder.lastName);
          stakeholderStore.setValue('title', this.newStakeholder.title);
          stakeholderStore.setValue('phone', this.newStakeholder.phone);
          stakeholderStore.setValue('emailAddress', this.newStakeholder.email);
          stakeholderStore.setValue('altEmailAddress', this.newStakeholder.altEmail);
          stakeholderStore.setValue('org', this.newStakeholder.organization);
          stakeholderStore.setValue('organizationRole', this.newStakeholder.organizationRole);
          stakeholderStore.setValue('name', `${this.newStakeholder.firstName} ${this.newStakeholder.lastName}`);

          const newStakeholder = await stakeholderStore.addStakeholder();
          if (newStakeholder && stakeholderListStore) {
            await stakeholderListStore.addStakeholder(newStakeholder);
          }
          this.setIsAddingNew(false);
        } catch (error) {
          console.error('Failed to add stakeholder:', error);
        }
      },

      async deleteStakeholder(id: string) {
        try {
          if (stakeholderListStore) {
            await stakeholderListStore.deleteStakeholder(id);
          }
        } catch (error) {
          console.error('Failed to delete stakeholder:', error);
        }
      },

      async updateStakeholder(id: string, field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) {
        try {
          // Map OpportunityStakeholder fields to UpdateStakeholderInput fields
          const fieldMapping: Record<keyof Omit<OpportunityStakeholder, 'id'>, string> = {
            firstName: 'firstName',
            lastName: 'lastName',
            title: 'title',
            phone: 'phone',
            email: 'emailAddress',
            altEmail: 'altEmailAddress',
            organization: 'org',
            organizationRole: 'organizationRole',
          };

          const updateInput: UpdateStakeholderInput = {
            [fieldMapping[field]]: value,
          };

          if (stakeholderListStore) {
            await stakeholderListStore.updateStakeholder(id, updateInput);
          }
        } catch (error) {
          console.error('Failed to update stakeholder:', error);
        }
      },
    }));

    const isStakeholderEditable = (stakeholderId: string) => {
      return editable && locals.editingStakeholderId === stakeholderId;
    };

    return (
      <Group title="Potential Stakeholders" style={{ zIndex: 2 }} description={label}>
        <View style={{ gap: 16 }}>
          {editable && !locals.isAddingNew && (
            <Button
              labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
              type="secondary"
              compact
              onPress={() => locals.setIsAddingNew(true)}
              iconName="plus-box-outline"
              iconRight
            >
              Add Stakeholder
            </Button>
          )}

          {locals.isAddingNew && (
            <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
              <StakeholderForm
                stakeholder={null}
                isNew={true}
                editable={true}
                getValue={(field: keyof Omit<OpportunityStakeholder, 'id'>) => locals.newStakeholder[field] || ''}
                setValue={(field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) =>
                  locals.updateNewStakeholder(field, value)
                }
                theme={theme}
              />
              <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                <Button
                  labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                  type="secondary"
                  compact
                  onPress={() => locals.setIsAddingNew(false)}
                >
                  Cancel
                </Button>
                <Button
                  labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                  type="primary"
                  compact
                  onPress={() => locals.addStakeholder()}
                >
                  Add
                </Button>
              </View>
            </View>
          )}
          {stakeholderListStore?.stakeholders.map((stakeholder) => (
            <View key={stakeholder.id} style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
              <View style={{ flexDirection: 'row', gap: 8, alignItems: 'flex-start' }}>
                <View style={{ flex: 1 }}>
                  <StakeholderForm
                    stakeholder={stakeholder as any}
                    isNew={false}
                    editable={isStakeholderEditable(stakeholder.id)}
                    getValue={(field: keyof Omit<OpportunityStakeholder, 'id'>) => {
                      // Map OpportunityStakeholder fields to Stakeholder fields
                      const fieldMapping: Record<keyof Omit<OpportunityStakeholder, 'id'>, string> = {
                        firstName: 'firstName',
                        lastName: 'lastName',
                        title: 'title',
                        phone: 'phone',
                        email: 'emailAddress',
                        altEmail: 'altEmailAddress',
                        organization: 'org',
                        organizationRole: 'organizationRole',
                      };
                      return (stakeholder as any)[fieldMapping[field]] || '';
                    }}
                    setValue={(field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) =>
                      locals.updateStakeholder(stakeholder.id, field, value)
                    }
                    theme={theme}
                  />
                  {editable && !isStakeholderEditable(stakeholder.id) && (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 8,
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        marginTop: 16,
                      }}
                    >
                      <Button
                        labelStyle={[
                          { textTransform: 'capitalize', color: colors.secondaryTextColor },
                          fonts.mediumTitle,
                        ]}
                        type="secondary"
                        compact
                        onPress={() => locals.setEditingStakeholderId(stakeholder.id)}
                      >
                        Edit
                      </Button>
                    </View>
                  )}
                  {editable && isStakeholderEditable(stakeholder.id) && (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 8,
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginTop: 16,
                      }}
                    >
                      <Button
                        labelStyle={[{ textTransform: 'capitalize', color: colors.error }, fonts.mediumTitle]}
                        type="secondary"
                        compact
                        onPress={() => locals.deleteStakeholder(stakeholder.id)}
                        iconName="delete"
                      >
                        Delete
                      </Button>
                      <View style={{ gap: 8, flexDirection: 'row' }}>
                        <Button
                          labelStyle={[
                            { textTransform: 'capitalize', color: colors.secondaryTextColor },
                            fonts.mediumTitle,
                          ]}
                          type="secondary"
                          compact
                          onPress={() => locals.setEditingStakeholderId(null)}
                        >
                          Cancel
                        </Button>
                        <Button
                          labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                          type="primary"
                          compact
                          onPress={() => locals.setEditingStakeholderId(null)}
                        >
                          Save
                        </Button>
                      </View>
                    </View>
                  )}
                </View>
              </View>
            </View>
          ))}
        </View>
      </Group>
    );
  }),
);
