import { View } from 'react-native';
import { Button } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Group } from '../../../../lib/ui/atoms/Group';
import { StakeholderForm } from './StakeholderForm';
import { OpportunityStore, StakeholderStore } from '../../../../stores';
import { UpdateOperator } from '../../../../services/codegen/types';

interface OpportunityStakeholderProps {
  editable: boolean;
  label: string;
  theme: ReactNativePaper.ThemeProp;
  stakeholderStore: StakeholderStore;
  opportunityStore: OpportunityStore;
  onStakeholderChanged: (
    operator: UpdateOperator,
    stakeholderId: string,
    opportunityStore: OpportunityStore,
  ) => Promise<void>;
}

export const OpportunityStakeholders = withTheme(
  observer(
    ({
      editable,
      label,
      theme,
      stakeholderStore,
      onStakeholderChanged,
      opportunityStore,
    }: OpportunityStakeholderProps) => {
      const { colors, fonts } = theme;

      const locals = useLocalObservable(() => ({
        editingStakeholderId: null as string | null,
        isAddingNew: false,
        setEditingStakeholderId(id: string | null) {
          this.editingStakeholderId = id;
        },
        setIsAddingNew(value: boolean) {
          this.isAddingNew = value;
          if (!value) {
            stakeholderStore.clearBuffer();
          }
        },
      }));
      async function addStakeholder() {
        try {
          const newStakeholder = await stakeholderStore.addStakeholder();
          if (newStakeholder) {
            await stakeholderStore.addStakeholder();
          }
          locals.setIsAddingNew(false);
          await onStakeholderChanged(UpdateOperator.Add, newStakeholder.id, opportunityStore);
        } catch (error) {
          console.error('Failed to add stakeholder:', error);
        }
      }

      async function deleteStakeholder(id: string) {
        await onStakeholderChanged(UpdateOperator.Rm, id, opportunityStore);
      }

      async function updateStakeholder(id: string) {
        await stakeholderStore.updateStakeholder(id);
      }
      const isStakeholderEditable = (stakeholderId: string) => {
        return editable && locals.editingStakeholderId === stakeholderId;
      };

      return (
        <Group title="Potential Stakeholders" style={{ zIndex: 2 }} description={label}>
          <View style={{ gap: 16 }}>
            {editable && !locals.isAddingNew && (
              <Button
                labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                type="secondary"
                compact
                onPress={() => locals.setIsAddingNew(true)}
                iconName="plus-box-outline"
                iconRight
              >
                Add Stakeholder
              </Button>
            )}

            {locals.isAddingNew && (
              <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
                <StakeholderForm isNew={true} editable={true} stakeholderStore={stakeholderStore} theme={theme} />
                <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => locals.setIsAddingNew(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                    type="primary"
                    compact
                    onPress={() => addStakeholder()}
                  >
                    Add
                  </Button>
                </View>
              </View>
            )}
            {opportunityStore.opportunity?.stakeholders.map((stakeholder) => (
              <View key={stakeholder.id} style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
                <View style={{ flexDirection: 'row', gap: 8, alignItems: 'flex-start' }}>
                  <View style={{ flex: 1 }}>
                    <StakeholderForm
                      stakeholder={stakeholder}
                      stakeholderStore={stakeholderStore}
                      isNew={false}
                      editable={isStakeholderEditable(stakeholder.id)}
                      theme={theme}
                    />
                    {editable && !isStakeholderEditable(stakeholder.id) && (
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 8,
                          justifyContent: 'flex-end',
                          alignItems: 'center',
                          marginTop: 16,
                        }}
                      >
                        <Button
                          labelStyle={[
                            { textTransform: 'capitalize', color: colors.secondaryTextColor },
                            fonts.mediumTitle,
                          ]}
                          type="secondary"
                          compact
                          onPress={() => {
                            locals.setEditingStakeholderId(stakeholder.id);
                            stakeholderStore.initializeBufferForEditing(stakeholder);
                          }}
                        >
                          Edit
                        </Button>
                      </View>
                    )}
                    {editable && isStakeholderEditable(stakeholder.id) && (
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 8,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginTop: 16,
                        }}
                      >
                        <Button
                          labelStyle={[{ textTransform: 'capitalize', color: colors.error }, fonts.mediumTitle]}
                          type="secondary"
                          compact
                          onPress={() => deleteStakeholder(stakeholder.id)}
                          iconName="delete"
                        >
                          Delete
                        </Button>
                        <View style={{ gap: 8, flexDirection: 'row' }}>
                          <Button
                            labelStyle={[
                              { textTransform: 'capitalize', color: colors.secondaryTextColor },
                              fonts.mediumTitle,
                            ]}
                            type="secondary"
                            compact
                            onPress={() => locals.setEditingStakeholderId(null)}
                          >
                            Cancel
                          </Button>
                          <Button
                            labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                            type="primary"
                            compact
                            onPress={() => {
                              updateStakeholder(stakeholder.id);
                              locals.setEditingStakeholderId(null);
                            }}
                          >
                            Save
                          </Button>
                        </View>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            ))}
          </View>
        </Group>
      );
    },
  ),
);
