import { View } from 'react-native';
import { Button, LabeledTextInput } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Group } from '../../../../lib/ui/atoms/Group';

interface OpportunityStakeholder {
  id: string;
  firstName?: string;
  lastName?: string;
  title?: string;
  phone?: string;
  email?: string;
  altEmail?: string;
  organization?: string;
  organizationRole?: string;
}

interface OpportunityStakeholderProps {
  editable: boolean;
  label: string;
  theme: ReactNativePaper.ThemeProp;
}

export const OpportunityStakeholders = withTheme(
  observer(({ editable, label, theme }: OpportunityStakeholderProps) => {
    const { colors, fonts } = theme;

    const locals = useLocalObservable(() => ({
      stakeholders: [
        {
          id: '1',
          firstName: 'Jarred',
          lastName: '<PERSON>na',
          title: 'Software Engineer',
          email: '<EMAIL>',
          organization: 'Somewhere',
          organizationRole: 'Software Engineer',
        },
        {
          id: '2',
          firstName: '<PERSON>',
          lastName: 'Doe',
          title: 'Manager',
          email: '<EMAIL>',
          organization: 'Example Corp',
          organizationRole: 'Project Manager',
        },
      ] as OpportunityStakeholder[],
      editingStakeholderId: null as string | null,
      isAddingNew: false,
      newStakeholder: {
        firstName: '',
        lastName: '',
        title: '',
        phone: '',
        email: '',
        altEmail: '',
        organization: '',
        organizationRole: '',
      } as Omit<OpportunityStakeholder, 'id'>,

      setEditingStakeholderId(id: string | null) {
        this.editingStakeholderId = id;
      },

      setIsAddingNew(value: boolean) {
        this.isAddingNew = value;
        if (!value) {
          this.resetNewStakeholder();
        }
      },

      resetNewStakeholder() {
        this.newStakeholder = {
          firstName: '',
          lastName: '',
          title: '',
          phone: '',
          email: '',
          altEmail: '',
          organization: '',
          organizationRole: '',
        };
      },

      updateNewStakeholder(field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) {
        this.newStakeholder[field] = value;
      },

      addStakeholder() {
        const newId = (Math.max(...this.stakeholders.map((s) => parseInt(s.id))) + 1).toString();
        this.stakeholders.push({
          id: newId,
          ...this.newStakeholder,
        });
        this.setIsAddingNew(false);
      },

      deleteStakeholder(id: string) {
        this.stakeholders = this.stakeholders.filter((s) => s.id !== id);
        if (this.editingStakeholderId === id) {
          this.editingStakeholderId = null;
        }
      },

      updateStakeholder(id: string, field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) {
        const stakeholder = this.stakeholders.find((s) => s.id === id);
        if (stakeholder) {
          stakeholder[field] = value;
        }
      },
    }));

    const isStakeholderEditable = (stakeholderId: string) => {
      return editable && locals.editingStakeholderId === stakeholderId;
    };

    return (
      <Group title="Potential Stakeholders" style={{ zIndex: 2 }} description={label}>
        <View style={{ gap: 16 }}>
          {/* Add New Stakeholder Button */}
          {editable && !locals.isAddingNew && (
            <Button
              labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
              type="secondary"
              compact
              onPress={() => locals.setIsAddingNew(true)}
              iconName="plus-box-outline"
              iconRight
            >
              Add Stakeholder
            </Button>
          )}

          {/* New Stakeholder Form */}
          {locals.isAddingNew && (
            <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
              {renderStakeholderForm(null, true)}
              <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                <Button
                  labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                  type="secondary"
                  compact
                  onPress={() => locals.setIsAddingNew(false)}
                >
                  Cancel
                </Button>
                <Button
                  labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                  type="primary"
                  compact
                  onPress={() => locals.addStakeholder()}
                >
                  Add
                </Button>
              </View>
            </View>
          )}
          {locals.stakeholders.map((stakeholder: OpportunityStakeholder) => (
            <View key={stakeholder.id} style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
              <View style={{ flexDirection: 'row', gap: 8, alignItems: 'flex-start' }}>
                <View style={{ flex: 1 }}>
                  {renderStakeholderForm(stakeholder)}
                  <View
                    style={{
                      flexDirection: 'row',
                      gap: 8,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginTop: 16,
                    }}
                  >
                    {editable && (
                      <Button
                        labelStyle={[{ textTransform: 'capitalize', color: colors.error }, fonts.mediumTitle]}
                        type="secondary"
                        compact
                        onPress={() => locals.deleteStakeholder(stakeholder.id)}
                        iconName="delete"
                        style={{ marginTop: 8 }}
                      >
                        Delete
                      </Button>
                    )}
                    {editable && !isStakeholderEditable(stakeholder.id) && (
                      <Button
                        labelStyle={[
                          { textTransform: 'capitalize', color: colors.secondaryTextColor },
                          fonts.mediumTitle,
                        ]}
                        type="secondary"
                        compact
                        onPress={() => locals.setEditingStakeholderId(stakeholder.id)}
                      >
                        Edit
                      </Button>
                    )}
                    {editable && isStakeholderEditable(stakeholder.id) && (
                      <>
                        <Button
                          labelStyle={[
                            { textTransform: 'capitalize', color: colors.secondaryTextColor },
                            fonts.mediumTitle,
                          ]}
                          type="secondary"
                          compact
                          onPress={() => locals.setEditingStakeholderId(null)}
                        >
                          Cancel
                        </Button>
                        <Button
                          labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                          type="primary"
                          compact
                          onPress={() => locals.setEditingStakeholderId(null)}
                        >
                          Save
                        </Button>
                      </>
                    )}
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
      </Group>
    );
  }),
);

const renderStakeholderForm = (stakeholder: OpportunityStakeholder | null, isNew = false) => {
  const getValue = (field: keyof Omit<OpportunityStakeholder, 'id'>) => {
    return isNew ? locals.newStakeholder[field] || '' : stakeholder?.[field] || '';
  };

  const setValue = (field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) => {
    if (isNew) {
      locals.updateNewStakeholder(field, value);
    } else if (stakeholder) {
      locals.updateStakeholder(stakeholder.id, field, value);
    }
  };

  return (
    <>
      <View style={{ flexDirection: 'row', gap: 8 }}>
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="First Name"
          textInputProps={{
            getValue: () => getValue('firstName'),
            setValue: (value) => setValue('firstName', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="Last Name"
          textInputProps={{
            getValue: () => getValue('lastName'),
            setValue: (value) => setValue('lastName', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
      </View>
      <View style={{ flexDirection: 'row', gap: 8 }}>
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="Rank / Title"
          textInputProps={{
            getValue: () => getValue('title'),
            setValue: (value) => setValue('title', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="Phone"
          textInputProps={{
            getValue: () => getValue('phone'),
            setValue: (value) => setValue('phone', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
      </View>
      <View style={{ flexDirection: 'row', gap: 8 }}>
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="Email"
          textInputProps={{
            getValue: () => getValue('email'),
            setValue: (value) => setValue('email', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="Alternate Email"
          textInputProps={{
            getValue: () => getValue('altEmail'),
            setValue: (value) => setValue('altEmail', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
      </View>
      <View style={{ flexDirection: 'row', gap: 8 }}>
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="Organization"
          textInputProps={{
            getValue: () => getValue('organization'),
            setValue: (value) => setValue('organization', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
        <LabeledTextInput
          style={{ flex: 1 }}
          labelText="Organization Role"
          textInputProps={{
            getValue: () => getValue('organizationRole'),
            setValue: (value) => setValue('organizationRole', value),
            editable: isNew || isStakeholderEditable(stakeholder?.id || ''),
          }}
        />
      </View>
    </>
  );
};
