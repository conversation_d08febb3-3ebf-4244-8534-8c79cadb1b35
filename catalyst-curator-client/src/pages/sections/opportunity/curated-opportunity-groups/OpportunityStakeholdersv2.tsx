import { View } from 'react-native';
import { Button, LabeledTextInput } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Group } from '../../../../lib/ui/atoms/Group';

interface OpportunityStakeholderProps {
  editable: boolean;
  label: string;
  theme: ReactNativePaper.ThemeProp;
}

export const OpportunityStakeholders = withTheme(
  observer(({ editable, label, theme }: OpportunityStakeholderProps) => {
    const { colors, fonts } = theme;
    const stakeholders: { id: string }[] = [{ id: '1' }, { id: '2' }];

    const locals = useLocalObservable(() => ({
      editingStakeholderId: null as string | null,
      setEditingStakeholderId(id: string | null) {
        this.editingStakeholderId = id;
      },
    }));
    const isStakeholderEditable = (stakeholderId: string) => {
      return editable && locals.editingStakeholderId === stakeholderId;
    };

    return (
      <Group title="Potential Stakeholders" style={{ zIndex: 2 }} description={label}>
        <View style={{ gap: 16 }}>
          {stakeholders.map((stakeholder) => {
            return (
              <View style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
                <View style={{ flexDirection: 'row', gap: 8 }}>
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="First Name"
                    textInputProps={{
                      getValue: () => 'Jarred',
                      setValue: () => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="Last Name"
                    textInputProps={{
                      getValue: () => 'Kalina',
                      setValue: (value) => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                </View>
                <View style={{ flexDirection: 'row', gap: 8 }}>
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="Rank / Title"
                    textInputProps={{
                      getValue: () => 'Software Engineer',
                      setValue: () => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="Phone"
                    textInputProps={{
                      getValue: () => '',
                      setValue: (value) => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                </View>
                <View style={{ flexDirection: 'row', gap: 8 }}>
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="Email"
                    textInputProps={{
                      getValue: () => '<EMAIL>',
                      setValue: () => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="Alternate Email"
                    textInputProps={{
                      getValue: () => '',
                      setValue: (value) => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                </View>
                <View style={{ flexDirection: 'row', gap: 8 }}>
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="Organization"
                    textInputProps={{
                      getValue: () => 'Somewhere',
                      setValue: () => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                  <LabeledTextInput
                    style={{ flex: 1 }}
                    labelText="Organization Role"
                    textInputProps={{
                      getValue: () => 'Software Engineer',
                      setValue: (value) => {},
                      editable: isStakeholderEditable(stakeholder.id),
                    }}
                  />
                </View>
                <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-end' }}>
                  {editable && !isStakeholderEditable(stakeholder.id) ? (
                    <Button
                      labelStyle={[
                        { textTransform: 'capitalize', color: colors.secondaryTextColor },
                        fonts.mediumTitle,
                      ]}
                      type="secondary"
                      compact
                      onPress={() => {
                        locals.setEditingStakeholderId(stakeholder.id);
                      }}
                    >
                      Edit
                    </Button>
                  ) : null}
                  {editable && isStakeholderEditable(stakeholder.id) ? (
                    <Button
                      labelStyle={[
                        { textTransform: 'capitalize', color: colors.secondaryTextColor },
                        fonts.mediumTitle,
                      ]}
                      type="secondary"
                      compact
                      onPress={() => {
                        locals.setEditingStakeholderId(null);
                      }}
                    >
                      Cancel
                    </Button>
                  ) : null}
                  {editable && isStakeholderEditable(stakeholder.id) ? (
                    <Button
                      labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                      type="primary"
                      compact
                      onPress={() => {
                        locals.setEditingStakeholderId(null);
                      }}
                    >
                      Save
                    </Button>
                  ) : null}
                </View>
              </View>
            );
          })}
        </View>
      </Group>
    );
  }),
);
