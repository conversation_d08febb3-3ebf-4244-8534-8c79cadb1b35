import { View } from 'react-native';
import { LabeledTextInput } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer } from 'mobx-react';
import { StakeholderStore } from '../../../../stores';
import { Stakeholder } from '../../../../services/codegen/types';

interface StakeholderFormProps {
  stakeholderStore: StakeholderStore;
  isNew?: boolean;
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  stakeholder?: Stakeholder;
}

export const StakeholderForm = withTheme(
  observer(({ stakeholderStore, isNew = false, editable, stakeholder }: StakeholderFormProps) => {
    const isFieldEditable = isNew || editable;

    const getValueWithPlaceholder = (fieldName: string, fallbackValue?: string | null) => {
      const value = stakeholderStore.getValueWithFallback(fieldName, fallbackValue || '');

      if (!isFieldEditable && (!value || value.trim() === '')) {
        return 'Not provided';
      }
      return value || '';
    };

    return (
      <>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="First Name"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('firstName', stakeholder?.firstName),
              setValue: (value) => {
                stakeholderStore.setValue('firstName', value);
              },
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Last Name"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('lastName', stakeholder?.lastName),
              setValue: (value) => stakeholderStore.setValue('lastName', value),
              editable: isFieldEditable,
            }}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Rank / Title"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('title', stakeholder?.title),
              setValue: (value) => stakeholderStore.setValue('title', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Phone"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('phone', stakeholder?.phone),
              setValue: (value) => stakeholderStore.setValue('phone', value),
              editable: isFieldEditable,
            }}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Email"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('emailAddress', stakeholder?.emailAddress),
              setValue: (value) => stakeholderStore.setValue('emailAddress', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Alternate Email"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('altEmailAddress', stakeholder?.altEmailAddress),
              setValue: (value) => stakeholderStore.setValue('altEmailAddress', value),
              editable: isFieldEditable,
            }}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Organization"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('org', stakeholder?.org),
              setValue: (value) => stakeholderStore.setValue('org', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Organization Role"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('organizationRole', stakeholder?.organizationRole),
              setValue: (value) => stakeholderStore.setValue('organizationRole', value),
              editable: isFieldEditable,
            }}
          />
        </View>
      </>
    );
  }),
);
