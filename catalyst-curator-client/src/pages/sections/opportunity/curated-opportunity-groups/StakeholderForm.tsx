import { View } from 'react-native';
import { LabeledTextInput, LabeledPhoneInput, HelperTextProps, LabeledDropdownMenu, MenuItem } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer } from 'mobx-react';
import { StakeholderStore } from '../../../../stores';
import { ProjectStakeholderType, Stakeholder } from '../../../../services/codegen/types';
import {
  getCountryCodeItem,
  getCountryCodeItems,
  onSelectCountryCode,
  setPhoneNumber,
  stripCountryCode,
} from '../../../../lib/phoneNumberUtils';

interface StakeholderFormProps {
  stakeholderStore: StakeholderStore;
  isNew?: boolean;
  theme: ReactNativePaper.ThemeProp;
  isEditing: boolean;
  stakeholder?: Stakeholder;
  isProjectStakeholder?: boolean;
  defaultType?: ProjectStakeholderType;
}

export const StakeholderForm = withTheme(
  observer(
    ({
      stakeholderStore,
      isNew = false,
      isEditing,
      stakeholder,
      isProjectStakeholder = false,
      defaultType,
    }: StakeholderFormProps) => {
      const isFieldEditable = isNew || isEditing;

      const getValueWithPlaceholder = (fieldName: string, fallbackValue?: string | null) => {
        if (isFieldEditable) {
          const bufferValue = stakeholderStore.getValue(fieldName);
          return bufferValue !== undefined ? bufferValue : fallbackValue || '';
        }
        const actualValue = fallbackValue || '';
        return actualValue.trim() === '' ? 'Not provided' : actualValue;
      };

      const getHelperTextProps = (fieldName: string): HelperTextProps => {
        return { type: 'error', children: stakeholderStore.getPropertyError(fieldName) };
      };

      function getStakeholderTypeLabel(type: ProjectStakeholderType) {
        const lowerCaseType = type.toLowerCase();
        return lowerCaseType.charAt(0).toUpperCase() + lowerCaseType.slice(1);
      }

      return (
        <>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="First Name"
              textInputProps={{
                getValue: () => {
                  console.log({
                    firstName: getValueWithPlaceholder('firstName', stakeholder?.firstName),
                    firstnameStore: stakeholderStore.getValue('firstName'),
                  });
                  return getValueWithPlaceholder('firstName', stakeholder?.firstName);
                },
                setValue: (value) => {
                  stakeholderStore.setValue('firstName', value);
                },
                editable: isFieldEditable,
              }}
              getHelperTextProps={() => getHelperTextProps('firstName')}
            />
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Last Name"
              textInputProps={{
                getValue: () => getValueWithPlaceholder('lastName', stakeholder?.lastName),
                setValue: (value) => stakeholderStore.setValue('lastName', value),
                editable: isFieldEditable,
              }}
            />
          </View>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Rank / Title"
              textInputProps={{
                getValue: () => getValueWithPlaceholder('title', stakeholder?.title),
                setValue: (value) => stakeholderStore.setValue('title', value),
                editable: isFieldEditable,
              }}
            />
            <LabeledPhoneInput
              style={{ flex: 1 }}
              countryCodeMenuProps={{
                getMenuItems: () => getCountryCodeItems(),
                getValue: () => getCountryCodeItem(stakeholderStore.getValue('phone') || stakeholder?.phone || ''),
                onItemSelected: (item) => {
                  onSelectCountryCode(
                    item,
                    stakeholderStore.setValue.bind(stakeholderStore),
                    stakeholderStore.getValue('phone') || stakeholder?.phone || '',
                  );
                },
                getEditable: () => isFieldEditable,
                anchorStyle: { height: 41, maxHeight: 41 },
              }}
              textInputProps={{
                editable: isFieldEditable,
                multiline: false,
                spellcheck: false,
                getValue: () => stripCountryCode(getValueWithPlaceholder('phone', stakeholder?.phone)),
                setValue: (value) =>
                  setPhoneNumber(
                    value,
                    stakeholderStore.setValue.bind(stakeholderStore),
                    stakeholderStore.getValue('phone') || stakeholder?.phone || '',
                  ),
                placeholder: '(*************',
                autoComplete: 'new-password',
              }}
              labelText="Phone"
              getHelperTextProps={() => getHelperTextProps('phone')}
            />
          </View>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Email"
              textInputProps={{
                getValue: () => getValueWithPlaceholder('emailAddress', stakeholder?.emailAddress),
                setValue: (value) => stakeholderStore.setValue('emailAddress', value),
                editable: isFieldEditable,
              }}
              getHelperTextProps={() => getHelperTextProps('emailAddress')}
            />
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Alternate Email"
              textInputProps={{
                getValue: () => getValueWithPlaceholder('altEmailAddress', stakeholder?.altEmailAddress),
                setValue: (value) => stakeholderStore.setValue('altEmailAddress', value),
                editable: isFieldEditable,
              }}
              getHelperTextProps={() => getHelperTextProps('altEmailAddress')}
            />
          </View>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Organization"
              textInputProps={{
                getValue: () => getValueWithPlaceholder('org', stakeholder?.org),
                setValue: (value) => stakeholderStore.setValue('org', value),
                editable: isFieldEditable,
              }}
            />
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Organization Role"
              textInputProps={{
                getValue: () => getValueWithPlaceholder('organizationRole', stakeholder?.organizationRole),
                setValue: (value) => stakeholderStore.setValue('organizationRole', value),
                editable: isFieldEditable,
              }}
            />
          </View>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            {isProjectStakeholder && (
              <LabeledDropdownMenu
                labelText="Stakeholder Type"
                dropdownMenuProps={{
                  getEditable: () => isFieldEditable,
                  getMenuItems: () => [
                    { label: 'Division', value: ProjectStakeholderType.Division },
                    { label: 'Performer', value: ProjectStakeholderType.Performer },
                    { label: 'Transition', value: ProjectStakeholderType.Transition },
                  ],
                  onItemSelected: (item: MenuItem) => {
                    stakeholderStore.setValue('type', item.value);
                  },
                  getValue: () => ({
                    label: getStakeholderTypeLabel(stakeholderStore.getValue('type') || defaultType),
                    value: stakeholderStore.getValue('type') || defaultType,
                  }),
                }}
              />
            )}
          </View>
        </>
      );
    },
  ),
);
