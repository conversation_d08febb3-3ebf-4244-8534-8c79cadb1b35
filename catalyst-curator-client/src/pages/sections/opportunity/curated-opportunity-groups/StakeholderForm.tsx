import { View } from 'react-native';
import { LabeledTextInput, LabeledPhoneInput, HelperTextProps } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer } from 'mobx-react';
import { StakeholderStore } from '../../../../stores';
import { ProjectStakeholderType, Stakeholder } from '../../../../services/codegen/types';
import {
  getCountryCodeItem,
  getCountryCodeItems,
  onSelectCountryCode,
  setPhoneNumber,
  stripCountryCode,
} from '../../../../lib/phoneNumberUtils';

interface StakeholderFormProps {
  stakeholderStore: StakeholderStore;
  isNew?: boolean;
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  stakeholder?: Stakeholder;
}

export const StakeholderForm = withTheme(
  observer(({ stakeholderStore, isNew = false, editable, stakeholder }: StakeholderFormProps) => {
    const isFieldEditable = isNew || editable;

    const getValueWithPlaceholder = (fieldName: string, fallbackValue?: string | null) => {
      if (!editable) return fallbackValue;
      const bufferValue = stakeholderStore.getValue(fieldName);
      const value = bufferValue !== undefined ? bufferValue : fallbackValue || '';
      if (!isFieldEditable && !value) {
        return 'Not provided';
      }
      return value || '';
    };

    const getHelperTextProps = (fieldName: string): HelperTextProps => {
      return { type: 'error', children: stakeholderStore.getPropertyError(fieldName) };
    };

    return (
      <>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="First Name"
            textInputProps={{
              getValue: () => {
                return getValueWithPlaceholder('firstName', stakeholder?.firstName);
              },
              setValue: (value) => {
                stakeholderStore.setValue('firstName', value);
              },
              editable: isFieldEditable,
            }}
            getHelperTextProps={() => getHelperTextProps('firstName')}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Last Name"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('lastName', stakeholder?.lastName),
              setValue: (value) => stakeholderStore.setValue('lastName', value),
              editable: isFieldEditable,
            }}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Rank / Title"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('title', stakeholder?.title),
              setValue: (value) => stakeholderStore.setValue('title', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledPhoneInput
            style={{ flex: 1 }}
            countryCodeMenuProps={{
              getMenuItems: () => getCountryCodeItems(),
              getValue: () => getCountryCodeItem(stakeholderStore.getValue('phone') || stakeholder?.phone || ''),
              onItemSelected: (item) => {
                onSelectCountryCode(
                  item,
                  stakeholderStore.setValue.bind(stakeholderStore),
                  stakeholderStore.getValue('phone') || stakeholder?.phone || '',
                );
              },
              getEditable: () => isFieldEditable,
              anchorStyle: { height: 41, maxHeight: 41 },
            }}
            textInputProps={{
              editable: isFieldEditable,
              multiline: false,
              spellcheck: false,
              getValue: () => stripCountryCode(getValueWithPlaceholder('phone', stakeholder?.phone)),
              setValue: (value) =>
                setPhoneNumber(
                  value,
                  stakeholderStore.setValue.bind(stakeholderStore),
                  stakeholderStore.getValue('phone') || stakeholder?.phone || '',
                ),
              placeholder: '(*************',
              autoComplete: 'new-password',
            }}
            labelText="Phone"
            getHelperTextProps={() => getHelperTextProps('phone')}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Email"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('emailAddress', stakeholder?.emailAddress),
              setValue: (value) => stakeholderStore.setValue('emailAddress', value),
              editable: isFieldEditable,
            }}
            getHelperTextProps={() => getHelperTextProps('emailAddress')}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Alternate Email"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('altEmailAddress', stakeholder?.altEmailAddress),
              setValue: (value) => stakeholderStore.setValue('altEmailAddress', value),
              editable: isFieldEditable,
            }}
            getHelperTextProps={() => getHelperTextProps('altEmailAddress')}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Organization"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('org', stakeholder?.org),
              setValue: (value) => stakeholderStore.setValue('org', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Organization Role"
            textInputProps={{
              getValue: () => getValueWithPlaceholder('organizationRole', stakeholder?.organizationRole),
              setValue: (value) => stakeholderStore.setValue('organizationRole', value),
              editable: isFieldEditable,
            }}
          />
        </View>
      </>
    );
  }),
);
