import { View } from 'react-native';
import { LabeledTextInput } from '../../../../lib';
import { withTheme } from 'react-native-paper';
import { observer } from 'mobx-react';

export interface OpportunityStakeholder {
  id: string;
  firstName?: string;
  lastName?: string;
  title?: string;
  phone?: string;
  email?: string;
  altEmail?: string;
  organization?: string;
  organizationRole?: string;
}

interface StakeholderFormProps {
  stakeholder: OpportunityStakeholder | null;
  isNew?: boolean;
  editable: boolean;
  getValue: (field: keyof Omit<OpportunityStakeholder, 'id'>) => string;
  setValue: (field: keyof Omit<OpportunityStakeholder, 'id'>, value: string) => void;
  theme: ReactNativePaper.ThemeProp;
}

export const StakeholderForm = withTheme(
  observer(({ stakeholder, isNew = false, editable, getValue, setValue }: StakeholderFormProps) => {
    const isFieldEditable = isNew || editable;

    return (
      <>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="First Name"
            textInputProps={{
              getValue: () => getValue('firstName'),
              setValue: (value) => setValue('firstName', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Last Name"
            textInputProps={{
              getValue: () => getValue('lastName'),
              setValue: (value) => setValue('lastName', value),
              editable: isFieldEditable,
            }}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Rank / Title"
            textInputProps={{
              getValue: () => getValue('title'),
              setValue: (value) => setValue('title', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Phone"
            textInputProps={{
              getValue: () => getValue('phone'),
              setValue: (value) => setValue('phone', value),
              editable: isFieldEditable,
            }}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Email"
            textInputProps={{
              getValue: () => getValue('email'),
              setValue: (value) => setValue('email', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Alternate Email"
            textInputProps={{
              getValue: () => getValue('altEmail'),
              setValue: (value) => setValue('altEmail', value),
              editable: isFieldEditable,
            }}
          />
        </View>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Organization"
            textInputProps={{
              getValue: () => getValue('organization'),
              setValue: (value) => setValue('organization', value),
              editable: isFieldEditable,
            }}
          />
          <LabeledTextInput
            style={{ flex: 1 }}
            labelText="Organization Role"
            textInputProps={{
              getValue: () => getValue('organizationRole'),
              setValue: (value) => setValue('organizationRole', value),
              editable: isFieldEditable,
            }}
          />
        </View>
      </>
    );
  }),
);
