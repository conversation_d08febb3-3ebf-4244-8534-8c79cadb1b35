import { observer } from 'mobx-react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Label, LabeledTextInput, TextInputProps } from '../../../lib';
import { Group } from '../../../lib/ui/atoms/Group';
import { Router } from '../../../platform/Router';
import { MainStackParamList } from '../../../routing/screens';
import { Opportunity, RelatedOpportunityType, UpdateOperator } from '../../../services/codegen/types';
import { CategoryStore, StakeholderStore, TenantStore, UserStore } from '../../../stores';
import OpportunityStore from '../../../stores/OpportunityStore';
import { CategorySelector } from '../CategorySelector';
import { CreateNewProject } from './CreateNewProject';
import { AdminDeleteOpportunity } from './DeleteOpportunity';
import { RelatedOpportunities } from './RelatedOpportunities';
import { ProjectPrimers } from './ProjectPrimers';
import { RelatedOpportunitySelectionType } from '../../../constants/RelatedOpportunity';
import { ProblemStatement } from './curated-opportunity-groups/ProblemStatement';
import { LegacyContent } from './curated-opportunity-groups/LegacyContent';
import { WarfightingFunction } from './curated-opportunity-groups/WarfightingFunction';
import { Attachments } from './curated-opportunity-groups/Attachments';
import { CustomFields } from './curated-opportunity-groups/CustomFields';
import { Campaign } from './curated-opportunity-groups/Campaign';
import { OpportunityStatus } from './curated-opportunity-groups/OpportunityStatus';
import { Priority } from './curated-opportunity-groups/Priority';
import { Visibility } from './curated-opportunity-groups/Visibility';
import { ArmyModernizationPriorities } from './curated-opportunity-groups/ArmyModernizationPriorities';
import { EchelonApplicability } from './curated-opportunity-groups/EchelonApplicability';
import { TransitionInContact } from './curated-opportunity-groups/TransitionInContact';
import { OpportunityOwners } from './curated-opportunity-groups/OpportunityOwners';
import { OpportunityOwnerStore } from '../../../stores/OpportunityOwnerStore';
import { Analysis } from './curated-opportunity-groups/Analysis';
import { OpportunityOwnerListStore } from '../../../stores/OpportunityOwnerListStore';
import { Requirement } from './curated-opportunity-groups/Requirement';
import { OpportunityStakeholders } from './curated-opportunity-groups/OpportunityStakeholders';

export interface CuratedOpportunityGroupProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  label: string;
}

interface CuratedOpportunityProps {
  tenantStore: TenantStore;
  opportunityStore: OpportunityStore;
  categoryStore: CategoryStore;
  stakeholderStore: StakeholderStore;
  opportunityOwnersStore: OpportunityOwnerStore;
  router: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  onProjectCreated: (projectId: string) => void;
  onSubmissionToggle: () => void;
  opportunityOwnerListStore: OpportunityOwnerListStore;
}

const readOnlyPlaceholder = 'No information provided';

export const CuratedOpportunity = withTheme(
  observer(
    ({
      categoryStore,
      onProjectCreated,
      onSubmissionToggle,
      opportunityStore,
      router,
      stakeholderStore,
      opportunityOwnersStore,
      tenantStore,
      userStore,
      opportunityOwnerListStore,
      theme,
    }: CuratedOpportunityProps) => {
      const { styles, fontSizes, fonts, colors } = theme;
      const { components, margins, paddings } = styles;
      const { editable } = opportunityStore;
      const { tenantConfig } = tenantStore;
      const { fields } = tenantConfig;

      const handleOnRelatedOpportunityChanged = async (
        operator: UpdateOperator,
        opportunity: Partial<Opportunity>,
        type: RelatedOpportunitySelectionType,
      ) => {
        const { id } = opportunity;
        if (type === RelatedOpportunitySelectionType.Parent) {
          await opportunityStore.updateOwningRelationship(id!, operator, RelatedOpportunityType.Child);
        } else if (type === RelatedOpportunitySelectionType.LinkedNotOwner) {
          await opportunityStore.updateOwningRelationship(id!, operator, RelatedOpportunityType.Linked);
        } else {
          const items = [{ id, type }];
          const objectUpdate: { [k: string]: any } = {};
          objectUpdate['relatedOpportunities'] = [{ operator, items }];
          await opportunityStore.updateOpportunityLinks(objectUpdate);
        }
      };

      const handleOnCategoryChanged = async (operator: UpdateOperator, id: string) => {
        const objectUpdate: { [k: string]: any } = {};
        objectUpdate['categories'] = [{ operator: operator, ids: [id] }];
        await opportunityStore.updateOpportunityLinks(objectUpdate);
      };

      return (
        <View style={[margins.BottomML, { flex: 1 }]}>
          <ProblemStatement
            editable={editable}
            getLabeledTextInput={getLabeledTextInput}
            label={labels['problem_statement']}
            onSubmissionToggle={onSubmissionToggle}
            opportunityStore={opportunityStore}
          />
          <Group title="Attributes" style={{ zIndex: 4 }} description={labels['attributes']}>
            <ArmyModernizationPriorities
              editable={editable}
              handleDebounce={handleDebounce}
              opportunityStore={opportunityStore}
            />
            <WarfightingFunction
              editable={editable}
              opportunityStore={opportunityStore}
              handleDebounce={handleDebounce}
              label={fields?.opportunity.function?.fieldCurationDescription || ''}
              fieldLabel={fields?.opportunity.function?.fieldLabel}
              functions={fields?.opportunity.function?.values}
            />
            <EchelonApplicability
              editable={editable}
              handleDebounce={handleDebounce}
              opportunityStore={opportunityStore}
            />
            <TransitionInContact
              editable={editable}
              handleDebounce={handleDebounce}
              opportunityStore={opportunityStore}
            />
            <CategorySelector
              onPropertyCategoryChanged={handleOnCategoryChanged}
              categoryStore={categoryStore}
              getCategories={() => opportunityStore.opportunity?.categories}
              getEditable={() => editable}
            />
          </Group>
          <Analysis
            editable={editable}
            label={labels['analysis']}
            opportunityStore={opportunityStore}
            handleDebounce={handleDebounce}
          />
          <Requirement
            editable={editable}
            label={labels['requirement']}
            opportunityStore={opportunityStore}
            handleDebounce={handleDebounce}
          />
          <OpportunityStakeholders editable={editable} label={labels['stakeholders']} />
          <Attachments
            editable={editable}
            label={labels['attachments']}
            opportunityStore={opportunityStore}
            getLabeledTextInput={getLabeledTextInput}
            userStore={userStore}
          />
          <CustomFields
            editable={editable}
            label={labels['custom_fields']}
            opportunityStore={opportunityStore}
            getLabeledTextInput={getLabeledTextInput}
          />
          <Campaign
            editable={editable}
            label={fields?.opportunity.campaign?.fieldCurationDescription || ''}
            opportunityStore={opportunityStore}
            fieldLabel={fields?.opportunity.campaign?.fieldLabel}
            getLabeledTextInput={getLabeledTextInput}
            campaigns={fields?.opportunity.campaign?.values}
            handleDebounce={handleDebounce}
          />
          <Group title="Related Opportunities" style={{ zIndex: 3 }} description={labels['related_opportunities']}>
            <RelatedOpportunities
              getOpportunityStore={() => opportunityStore}
              onUpdateOpportunity={handleOnRelatedOpportunityChanged}
              onPressOpportunity={(id: string) => handlePressOpportunity(id, router)}
              getEditable={() => editable}
            />
          </Group>
          <OpportunityOwners
            ownerListStore={opportunityOwnerListStore}
            userStore={userStore}
            editable={editable}
            label={labels['opportunity_owners']}
            opportunityStore={opportunityStore}
            opportunityOwnersStore={opportunityOwnersStore}
          />
          <View style={[components.infoSectionStyle, margins.BottomL, paddings.TopL]}>
            <LegacyContent
              editable={editable}
              getLabeledTextInput={getLabeledTextInput}
              label={labels['legacy_content']}
              opportunityStore={opportunityStore}
            />
            <OpportunityStatus
              editable={editable}
              label={labels['status']}
              opportunityStore={opportunityStore}
              handleDebounce={handleDebounce}
              getLabeledTextInput={getLabeledTextInput}
            />
            <Priority
              editable={editable}
              label={labels['priority']}
              opportunityStore={opportunityStore}
              handleDebounce={handleDebounce}
              getLabeledTextInput={getLabeledTextInput}
            />
            <Visibility
              editable={editable}
              handleDebounce={handleDebounce}
              label={labels['visibility']}
              opportunityStore={opportunityStore}
            />
            <Group title="Project Primers" description={labels['project_primers']} noBorder={true}>
              <Label textStyle={[margins.BottomM]}>Linked Project Primers</Label>
              <ProjectPrimers
                opportunityStore={opportunityStore}
                onPressProject={(id) => handlePressProject(id, router)}
              />
              <CreateNewProject
                style={[margins.LeftS, margins.TopL, margins.BottomM, { alignSelf: 'flex-start' }]}
                opportunityStore={opportunityStore}
                buttonStyle={[{}]}
                onProjectCreated={onProjectCreated}
              />
            </Group>
          </View>
          <AdminDeleteOpportunity
            userStore={userStore}
            opportunityStore={opportunityStore}
            onDeleteOpportunity={() => handleDeleteOpportunity(opportunityStore, router)}
            style={[{ alignSelf: 'center' }]}
          />
        </View>
      );
    },
  ),
);

const handlePressProject = (projectId: string, router?: Router<MainStackParamList>) => {
  router?.navigate('project', { id: projectId });
};

const handlePressOpportunity = (id: string, router?: Router<MainStackParamList>) => {
  router?.navigate('curation', { id });
};

const handleDeleteOpportunity = async (opportunityStore: OpportunityStore, router: Router<MainStackParamList>) => {
  await opportunityStore.softDeleteOpportunity();
  router.goBack();
};
const handleOnStakeholderChanged = async (operator: UpdateOperator, id: string, opportunityStore: OpportunityStore) => {
  const objectUpdate: { [k: string]: any } = {};
  objectUpdate['stakeholders'] = [{ operator, ids: [id] }];
  await opportunityStore.updateOpportunityLinks(objectUpdate);
};

export interface LabeledTextInputProps {
  opportunityStore: OpportunityStore;
  fieldName: string;
  labelText: string;
  style?: StyleProp<ViewStyle>;
  textInputProps: Partial<TextInputProps>;
  editable?: boolean;
}
const getLabeledTextInput = ({
  opportunityStore,
  fieldName,
  labelText,
  style,
  editable,
  textInputProps,
}: LabeledTextInputProps) => {
  return (
    <LabeledTextInput
      style={style}
      textInputProps={{
        multiline: true,
        spellcheck: false,
        getValue: () => opportunityStore.getValue(fieldName),
        setValue: (value) => opportunityStore.setValue(fieldName, value),
        onDebounceValue: (value) => handleDebounce(opportunityStore),
        editable: editable,
        readOnlyPlaceholder,
        ...textInputProps,
      }}
      labelText={labelText}
      key={fieldName}
    />
  );
};

const handleDebounce = (opportunityStore: OpportunityStore): void => {
  opportunityStore.saveOpportunity();
};

const labels = {
  problem_statement:
    'Complete the fields to the right to describe the problem the soldier wishes to input. Describe the overall problem and its impact at the unit level. Provide details on the scope of the problem, its impact on the unit (e.g. costs, lost time, safety considerations, etc.), and anticipated challenges to solution pathways.',
  legacy_content: 'Complete the fields to describe the proposed solution pathway.',
  stakeholders:
    'Use the fields to identify potential stakeholders for this engagement. Provide contact information, organization, and position.',
  categories: 'Use the fields to identify a pre-determined category for the problem, or to create a new category.',
  attachments:
    'Attachments may include documents, images, links or data files that provide  additional context or evidence related to the problem or a proposed solution.',
  custom_fields:
    'OPTIONAL: Use these fields (as applicable) to associate problem with an existing initiative or LOE, and to list existing endorsers/approvers.',
  related_opportunities:
    'Link related opportunities, with specific focus on problem and solution areas that may overlap with the current proposal.',
  status: 'Use the drop-down menu to describe the current status of this opportunity.',
  priority: 'Use the drop-down menu to select the unit priority level for this problem.',
  project_primers: 'Provide as much information on the engagement as you can.',
  visibility:
    'Marking this as Private will restrict visibility to your tenant only. Leave unchecked to share externally.',
  attributes: 'Use the fields to identify a pre-determined category for the problem, or to create a new category.',
  opportunity_owners:
    'This may be the original submitter or a unit-designated point of contact who ensures continuity, accountability, and forward progress of the submission.',
  analysis:
    'Clearly summarize the opportunity feasibility based on the use-case, available resources, stakeholder alignment, and potential for prototyping or adoption. Describe alignment to the operational problem, who is affected, and why action is needed now. Select the relevant solution type and affected DOTMLPF-P area(s). Identify any known efforts to solve the problem.',
  requirement:
    'This section allows users to add a specific requirement if one exists, and enables selection of the appropriate Capability Development Integration Directorate (CDID) and the relevant Army Capability Manager (ACM). \n\n Indicate whether this opportunity aligns with an existing Army Capability Requirement (e.g., ACIDS, JCIDS, or other validated needs).  Identifying a requirement connection helps determine eligibility for formal transition pathways and highlights alignment with Army modernization priorities. If no requirement is known, select "No Known Requirement" to flag a potential gap or emerging need.',
};
