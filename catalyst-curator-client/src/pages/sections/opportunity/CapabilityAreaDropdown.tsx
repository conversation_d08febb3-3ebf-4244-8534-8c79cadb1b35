import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { DependentDropdown, MenuGroup } from '../../../lib';
import { OpportunityStore } from '../../../stores';

interface CapabilityAreaDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const CapabilityAreaItems: MenuGroup[] = [
  { item: { label: 'Aircraft', value: 'Aircraft' } },
  { item: { label: 'Air Defense', value: 'Air Defense' } },
  { item: { label: 'AR/VR', value: 'AR/VR' } },
  { item: { label: 'Artificial Intelligence (AI)', value: 'Artificial Intelligence (AI)' } },
  { item: { label: 'Automation (Process)', value: 'Automation (Process)' } },
  { item: { label: 'Ammunition (Munitions)', value: 'Ammunition (Munitions)' } },
  { item: { label: 'Command and Control (C2)', value: 'Command and Control (C2)' } },
  { item: { label: 'Communications', value: 'Communications' } },
  { item: { label: 'Counter-Mobility', value: 'Counter-Mobility' } },
  { item: { label: 'Cover and Concealment', value: 'Cover and Concealment' } },
  { item: { label: 'Data Analytics and Visualization', value: 'Data Analytics and Visualization' } },
  { item: { label: 'Direct Fires', value: 'Direct Fires' } },
  { item: { label: 'Doctrine, Policy, and Organization', value: 'Doctrine, Policy, and Organization' } },
  { item: { label: 'Electronic Warfare (EW)', value: 'Electronic Warfare (EW)' } },
  { item: { label: 'Energy and Power', value: 'Energy and Power' } },
  { item: { label: 'Facilities', value: 'Facilities' } },
  { item: { label: 'Ground Vehicles', value: 'Ground Vehicles' } },
  { item: { label: 'Human Performance', value: 'Human Performance' } },
  { item: { label: 'Fitness / Wellness', value: 'Fitness / Wellness' } },
  { item: { label: 'Indirect Fires', value: 'Indirect Fires' } },
  { item: { label: 'ISR', value: 'ISR' } },
  { item: { label: 'Logistics', value: 'Logistics' } },
  { item: { label: 'Low-Cost Fabrication', value: 'Low-Cost Fabrication' } },
  { item: { label: 'Maintenance', value: 'Maintenance' } },
  { item: { label: 'Medical', value: 'Medical' } },
  //   copy the rest from above
  { item: { label: 'Mobility', value: 'Mobility' } },
  { item: { label: 'Network', value: 'Network' } },
  { item: { label: 'Nett Warrior', value: 'Nett Warrior' } },
  { item: { label: 'Other', value: 'Other' } },
  { item: { label: 'Personnel (Retention)', value: 'Personnel (Retention)' } },
  { item: { label: 'Personnel (Protection and Survivability)', value: 'Personnel (Protection and Survivability)' } },
  { item: { label: 'Reliability', value: 'Reliability' } },
  {
    item: {
      label: 'Robotic and Autonomous Systems Common Control',
      value: 'Robotic and Autonomous Systems Common Control',
    },
  },
  { item: { label: 'Sensors', value: 'Sensors' } },
  { item: { label: 'Soldier Worn / Carried', value: 'Soldier Worn / Carried' } },
  { item: { label: 'Soldier Load Reduction', value: 'Soldier Load Reduction' } },
  { item: { label: 'Soldier Common CsUAS', value: 'Soldier Common CsUAS' } },
  { item: { label: 'Soldier and Small Unit Power (S2UP)', value: 'Soldier and Small Unit Power (S2UP)' } },
  { item: { label: 'Sustainment', value: 'Sustainment' } },
  { item: { label: 'Training', value: 'Training' } },
  { item: { label: 'Tactical Assault Kit (TAK)', value: 'Tactical Assault Kit (TAK)' } },
  { item: { label: 'Unmanned Systems (UxS)', value: 'Unmanned Systems (UxS)' } },
  {
    item: { label: 'Aircraft System - UAS', value: 'Aircraft System - UAS' },
    children: [
      { item: { label: 'Aerial/Aircraft Vehicles - UAVs', value: 'Aerial/Aircraft Vehicles - UAVs' } },
      { item: { label: 'Combat Air System - UCAS', value: 'Combat Air System - UCAS' } },
      { item: { label: 'Ground Vehicles - UGVs', value: 'Ground Vehicles - UGVs' } },
      { item: { label: 'Mobile Vehicles - UMVs', value: 'Mobile Vehicles - UMVs' } },
      { item: { label: 'Surface Vehicles - USVs', value: 'Surface Vehicles - USVs' } },
      { item: { label: 'Undersea Vehicles - UUVs', value: 'Undersea Vehicles - UUVs' } },
      { item: { label: 'Underwater Vehicles - UWVs', value: 'Underwater Vehicles - UWVs' } },
    ],
  },
];

export const CapabilityAreaDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: CapabilityAreaDropdownProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <View style={[components.rowStyle]}>
        <Target name={'CapabilityArea'}>
          <DependentDropdown
            defaultValues={[
              {
                fieldName: 'capabilityAreaLevel1',
                label: 'Capability Area',
                value: {
                  label:
                    (opportunityStore.opportunity?.capabilityArea && opportunityStore.opportunity.capabilityArea[0]) ||
                    'Unassigned',
                  value:
                    (opportunityStore.opportunity?.capabilityArea && opportunityStore.opportunity.capabilityArea[0]) ||
                    'unassigned',
                },
              },
              {
                fieldName: 'capabilityAreaLevel2',
                label: undefined,
                value:
                  opportunityStore.opportunity?.capabilityArea && opportunityStore.opportunity.capabilityArea[1]
                    ? {
                        label: opportunityStore.opportunity.capabilityArea[1],
                        value: opportunityStore.opportunity.capabilityArea[1],
                      }
                    : undefined,
              },
            ]}
            getMenuGroups={() => {
              return CapabilityAreaItems;
            }}
            onItemSelected={(item, fieldName) => {
              const currentAreas = opportunityStore.opportunity?.capabilityArea || [];
              let newAreas = [...currentAreas];

              if (fieldName === 'capabilityAreaLevel1') {
                // Set level 1 and clear level 2
                newAreas = item?.value ? [item.value] : [];
              } else if (fieldName === 'capabilityAreaLevel2') {
                // Set level 2, keeping level 1
                if (newAreas.length > 0 && item?.value) {
                  newAreas = [newAreas[0], item.value];
                }
              }

              opportunityStore.setValue('capabilityArea', newAreas);
              handleDebounce(opportunityStore);
            }}
            labeledDropdownMenuProps={{
              dropdownMenuProps: {
                getEditable: () => editable,
                getMenuItems: () => [],
                onItemSelected: () => {},
              },
            }}
          />
        </Target>
      </View>
    );
  }),
);
