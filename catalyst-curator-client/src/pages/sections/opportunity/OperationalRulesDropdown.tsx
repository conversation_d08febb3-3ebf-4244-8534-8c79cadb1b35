import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { DependentDropdown, MenuGroup } from '../../../lib';
import { OpportunityStore } from '../../../stores';

interface OperationalRulesDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const OperationalRulesItems: MenuGroup[] = [
  {
    item: { label: 'Combat Arms', value: 'Combat Arms' },
    children: [
      { item: { label: 'Infantry', value: 'Infantry' } },
      { item: { label: 'Armor', value: 'Armor' } },
      { item: { label: 'Field Artillery', value: 'Field Artillery' } },
      { item: { label: 'Air Defense Artillery', value: 'Air Defense Artillery' } },
      { item: { label: 'Aviation', value: 'Aviation' } },
      { item: { label: 'Special Ops', value: 'Special Ops' } },
    ],
  },
  {
    item: { label: 'Combat Support', value: 'Combat Support' },
    children: [
      { item: { label: 'Military Intelligence', value: 'Military Intelligence' } },
      { item: { label: 'Signal Corps', value: 'Signal Corps' } },
      { item: { label: 'Chemical Corps', value: 'Chemical Corps' } },
      { item: { label: 'Military Police', value: 'Military Police' } },
      { item: { label: 'Engineers', value: 'Engineers' } },
    ],
  },
  {
    item: { label: 'Combat Service Support', value: 'Combat Service Support' },
    children: [
      { item: { label: 'Logistics Corps', value: 'Logistics Corps' } },
      { item: { label: 'Transportation Corps', value: 'Transportation Corps' } },
      { item: { label: 'Medical Corps', value: 'Medical Corps' } },
      { item: { label: 'Ordnance Corps', value: 'Ordnance Corps' } },
      { item: { label: 'Quartermaster Corps', value: 'Quartermaster Corps' } },
      { item: { label: 'Finance Corps', value: 'Finance Corps' } },
      { item: { label: "Adjutant General's Corps", value: "Adjutant General's Corps" } },
    ],
  },
];
export const OperationalRulesDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: OperationalRulesDropdownProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <View style={[components.rowStyle]}>
        <Target name={'OperationalRules'}>
          <DependentDropdown
            defaultValues={[
              {
                fieldName: 'operationalRulesLevel1',
                label: 'Operational Rules',
                value: {
                  label:
                    (opportunityStore.opportunity?.operationalRules &&
                      opportunityStore.opportunity.operationalRules[0]) ||
                    'Unassigned',
                  value:
                    (opportunityStore.opportunity?.operationalRules &&
                      opportunityStore.opportunity.operationalRules[0]) ||
                    'unassigned',
                },
              },
              {
                fieldName: 'operationalRulesLevel2',
                label: undefined,
                value:
                  opportunityStore.opportunity?.operationalRules && opportunityStore.opportunity.operationalRules[1]
                    ? {
                        label: opportunityStore.opportunity.operationalRules[1],
                        value: opportunityStore.opportunity.operationalRules[1],
                      }
                    : undefined,
              },
            ]}
            getMenuGroups={() => {
              return OperationalRulesItems;
            }}
            onItemSelected={(item, fieldName) => {
              const currentRules = opportunityStore.opportunity?.operationalRules || [];
              let newRules = [...currentRules];

              if (fieldName === 'operationalRulesLevel1') {
                // Set level 1 and clear level 2
                newRules = item?.value ? [item.value] : [];
              } else if (fieldName === 'operationalRulesLevel2') {
                // Set level 2, keeping level 1
                if (newRules.length > 0 && item?.value) {
                  newRules = [newRules[0], item.value];
                }
              }

              opportunityStore.setValue('operationalRules', newRules);
              handleDebounce(opportunityStore);
            }}
            labeledDropdownMenuProps={{
              dropdownMenuProps: {
                getEditable: () => editable,
                getMenuItems: () => [],
                onItemSelected: () => {},
              },
            }}
          />
        </Target>
      </View>
    );
  }),
);
