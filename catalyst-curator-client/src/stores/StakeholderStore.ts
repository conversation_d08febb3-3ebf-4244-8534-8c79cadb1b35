import { action, computed, makeObservable, observable, override } from 'mobx';
import { Store } from '../lib/stores/Store';
import {
  CreateStakeholderInput,
  SearchField,
  SearchOperator,
  Stakeholder,
  UpdateStakeholderInput,
} from '../services/codegen/types';
import { stakeholderService } from '../services/StakeholderService';

const DEFAULT_PAGE_SIZE = 10;

interface SearchValue {
  fieldName: string;
  value?: string;
}

interface SortValue {
  fieldName: string;
}
export class StakeholderStore extends Store {
  constructor() {
    super();
    makeObservable(this, {});
  }

  async localClearAll(): Promise<void> {
    this.searchStores = {};
  }
  protected async localInitialize(): Promise<void> {}

  getStakeholder(input: { id?: string; name?: string; org?: string }): Promise<Stakeholder> {
    return this.call(async () => stakeholderService.getStakeholder(input));
  }

  async addStakeholder(stakeholder: CreateStakeholderInput): Promise<Stakeholder> {
    return this.call(async () => stakeholderService.createStakeholder(stakeholder));
  }

  async updateStakeholder(input: UpdateStakeholderInput): Promise<Stakeholder> {
    return this.call(async () => stakeholderService.updateStakeholder(input));
  }

  async deleteStakeholder(id: string): Promise<boolean> {
    return this.call(async () => stakeholderService.deleteStakeholder(id));
  }
}
