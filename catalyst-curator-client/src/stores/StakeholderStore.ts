import { action, makeObservable, observable, runInAction } from 'mobx';
import { Store } from '../lib/stores/Store';
import {
  CreateStakeholderInput,
  PageInfo,
  PagingInput,
  SearchOperator,
  SearchSortInput,
  Stakeholder,
  UpdateStakeholderInput,
} from '../services/codegen/types';
import { stakeholderService } from '../services/StakeholderService';

export class StakeholderStore extends Store {
  static readonly DEFAULT_PAGE_SIZE = 500;
  static readonly DEFAULT_SEARCH_SORT: SearchSortInput = {
    sortFields: [{ fieldName: 'lastName', ascending: true }],
  };

  private _stakeholdersSearchResult: Stakeholder[] = [];
  private _pageInfo: PageInfo = {} as PageInfo;
  private _searchSortInput: SearchSortInput = StakeholderStore.DEFAULT_SEARCH_SORT;
  private _pagingInput: PagingInput = { pageSize: StakeholderStore.DEFAULT_PAGE_SIZE, cursor: '0' };
  private _keywordSearchValue: string = '';
  private _buffer: Record<string, any> = {};
  private _stakeholderInput: Partial<CreateStakeholderInput> = {};

  constructor() {
    super();
    makeObservable<
      StakeholderStore,
      '_stakeholdersSearchResult' | '_pageInfo' | '_keywordSearchValue' | '_stakeholderInput' | '_buffer'
    >(this, {
      _stakeholdersSearchResult: observable.shallow,
      _stakeholderInput: observable,
      _buffer: observable,
      _pageInfo: observable,
      _keywordSearchValue: observable,
      searchStakeholders: action,
      addStakeholder: action,
      setSelectedSearchStakeholderResult: action,
    });
  }

  setSelectedSearchStakeholderResult(value: Stakeholder) {
    this.setValue('firstName', value.firstName);
    this.setValue('lastName', value.lastName);
    this.setValue('emailAddress', value.emailAddress);
    this.setValue('phone', value.phone);
    this.setValue('org', value.org);
    this.setValue('altEmailAddress', value.altEmailAddress);
    this.setValue('title', value.title);
    this.setValue('organizationRole', value.organizationRole);
    this.setValue('name', value.name);
    this.setValue('stakeholderId', value.id);
  }

  get stakeholderInput(): CreateStakeholderInput {
    return { ...this._stakeholderInput, ...(this._buffer as any) } as CreateStakeholderInput;
  }

  get stakeholdersSearchResult() {
    return this._stakeholdersSearchResult;
  }

  validateAll(): boolean {
    return super.validateAll(this._buffer);
  }

  get pageInfo(): PageInfo {
    return this._pageInfo;
  }

  set keywordSearchValue(value: string) {
    this._keywordSearchValue = value;
    this._searchSortInput = !!value
      ? {
          searchFields: [
            {
              fieldNames: ['firstName', 'lastName', 'emailAddress'],
              operator: SearchOperator.Match,
              searchValue: value,
            },
          ],
        }
      : StakeholderStore.DEFAULT_SEARCH_SORT;
  }

  get keywordSearchValue(): string {
    return this._keywordSearchValue;
  }

  async searchStakeholders(): Promise<Stakeholder[]> {
    return this.call<Stakeholder[]>(async () => {
      const results = await stakeholderService.queryStakeholders({
        searchSortInput: this._searchSortInput,
        pagingInput: this._pagingInput,
      });
      runInAction(() => {
        this._stakeholdersSearchResult = results.map((r) => r).filter(Boolean) as Stakeholder[];
        // Note: StakeholderPage doesn't have pageInfo in the current schema, so we'll set a default
        this._pageInfo = {
          hasNext: false,
          hasPrevious: false,
          lastCursor: '0',
          lastPageSize: 0,
          retrievedCount: results.length,
          totalCount: results.length,
        };
      });
      return results;
    });
  }

  async addStakeholder(): Promise<Stakeholder> {
    return this.call(async () => stakeholderService.createStakeholder({ ...this.stakeholderInput }));
  }

  async updateStakeholder(id: string, input: UpdateStakeholderInput): Promise<Stakeholder> {
    return this.call(async () => stakeholderService.updateStakeholder({ id, input }));
  }

  async deleteStakeholder(id: string): Promise<boolean> {
    return this.call(async () => stakeholderService.deleteStakeholder(id));
  }

  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  getValue(name: string, defaultValue?: any) {
    const value = (this.stakeholderInput as any)?.[name];
    return value || defaultValue;
  }

  protected async localInitialize(): Promise<void> {}

  async localClearAll(): Promise<void> {
    this._stakeholdersSearchResult = [];
    this._pageInfo = {} as PageInfo;
    this._searchSortInput = StakeholderStore.DEFAULT_SEARCH_SORT;
    this._pagingInput = { pageSize: StakeholderStore.DEFAULT_PAGE_SIZE, cursor: '0' };
    this._keywordSearchValue = '';
    this._buffer = {};
    this._stakeholderInput = {};
  }
}
