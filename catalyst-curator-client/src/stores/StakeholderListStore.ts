import { action, computed, makeObservable, observable } from 'mobx';
import { Store } from '../lib/stores/Store';
import { Stakeholder, UpdateStakeholderInput } from '../services/codegen/types';
import { stakeholderService } from '../services/StakeholderService';

export class StakeholderListStore extends Store {
  private _stakeholders: Stakeholder[] = [];

  constructor() {
    super();
    this.localInitialize();
    makeObservable<StakeholderListStore, '_stakeholders'>(this, {
      _stakeholders: observable,
      setStakeholders: action,
      localClearAll: action,
      refresh: action,
      updateStakeholder: action,
      stakeholders: computed,
    });
  }

  get stakeholders(): Stakeholder[] {
    return this._stakeholders;
  }

  setStakeholders(stakeholders: Stakeholder[]) {
    this._stakeholders = stakeholders;
  }

  async refresh(): Promise<void> {
    try {
      const results = await stakeholderService.queryStakeholders({
        pagingInput: { pageSize: 500 },
        searchSortInput: {
          sortFields: [{ fieldName: 'lastName', ascending: true }],
        },
      });
      this.setStakeholders(results);
    } catch (error) {
      this.addError(error as any, false);
    }
  }

  async updateStakeholder(id: string, input: UpdateStakeholderInput): Promise<Stakeholder | null> {
    try {
      const updatedStakeholder = await stakeholderService.updateStakeholder({ id, input });
      const index = this._stakeholders.findIndex((s) => s.id === id);
      if (index !== -1) {
        this._stakeholders[index] = updatedStakeholder;
      }
      return updatedStakeholder;
    } catch (error) {
      this.addError(error as any, false);
      return null;
    }
  }

  async deleteStakeholder(id: string): Promise<boolean> {
    try {
      const success = await stakeholderService.deleteStakeholder(id);
      if (success) {
        this._stakeholders = this._stakeholders.filter((s) => s.id !== id);
      }
      return success;
    } catch (error) {
      this.addError(error as any, false);
      return false;
    }
  }

  async addStakeholder(stakeholder: Stakeholder): Promise<void> {
    this._stakeholders.push(stakeholder);
  }

  protected async localInitialize(): Promise<void> {}

  async localClearAll(): Promise<void> {
    this._stakeholders = [];
  }
}
