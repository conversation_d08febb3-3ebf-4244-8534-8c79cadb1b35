
export class StakeholderSearchStore extends Store {
  private _matchedStakeholders: Array<Stakeholder> = [];
  private _searchValues: Record<string, string | undefined> = {};
  private _sortValue: SortValue = { fieldName: 'name' };
  private _pageSize: number = DEFAULT_PAGE_SIZE;

  constructor() {
    super();
    makeObservable<StakeholderSearchStore, '_matchedStakeholders' | '_searchValues' | '_pageSize' | '_sortValue'>(
      this,
      {
        _matchedStakeholders: observable,
        _searchValues: observable,
        _sortValue: observable,
        _pageSize: observable,
        queryStakeholders: action,
        sortField: computed,
        pageSize: computed,
        matchedStakeholders: computed,
        clearAll: override,
        clearCache: override,
        restore: override,
      },
    );
  }

  get matchedStakeholders(): Array<Stakeholder> {
    return this._matchedStakeholders;
  }

  getMatchedStakeholderById(id: string) {
    return this.matchedStakeholders.find((stakeholder) => stakeholder.id === id);
  }

  getMatchedStakeholder(fieldValues: SearchValue[]) {
    return this.matchedStakeholders.find((matchStakeholder) => {
      fieldValues.every((fieldValue) => {
        (matchStakeholder as any)[fieldValue.fieldName] === fieldValue.value ||
          (!(matchStakeholder as any)[fieldValue.fieldName] && !fieldValue.value);
      });
    });
  }

  uniqueMatches(fieldName: string): string[] {
    return this.matchedStakeholders.reduce((accum, stakeholder) => {
      const value = (stakeholder as any)[fieldName];
      if (value && !accum.includes(value)) accum.push(value);
      return accum;
    }, [] as string[]);
  }

  setFieldSearchValue(fieldSearchValue: SearchValue) {
    const { fieldName, value } = fieldSearchValue;
    this._searchValues[fieldName] = value;
  }

  getSearchValue(fieldName: string): string | undefined {
    return this._searchValues[fieldName];
  }

  set sortField(sortField: string) {
    this._sortValue.fieldName = sortField;
  }

  get sortField(): string {
    return this._sortValue.fieldName;
  }

  set pageSize(pageSize: number) {
    this._pageSize = pageSize;
  }

  get pageSize() {
    return this._pageSize;
  }

  async localClearAll(): Promise<void> {
    this._matchedStakeholders = [];
    this._searchValues = {};
    this._sortValue = { fieldName: 'name' };
    this._pageSize = DEFAULT_PAGE_SIZE;
  }
  protected async localInitialize(): Promise<void> {}

  async queryStakeholders(): Promise<void> {
    const searchFields: SearchField[] = [];
    Object.keys(this._searchValues).forEach((searchFieldName) => {
      if (this._searchValues[searchFieldName])
        searchFields.push({
          fieldNames: [searchFieldName],
          operator: SearchOperator.Match,
          searchValue: '^' + this._searchValues[searchFieldName],
        });
    });
    return searchFields.length
      ? this.call(async () => {
          this._matchedStakeholders = await stakeholderService.queryStakeholders({
            pagingInput: { pageSize: this._pageSize },
            searchSortInput: {
              searchFields,
              sortFields: [{ fieldName: this.sortField, ascending: true }],
            },
          });
        })
      : undefined;
  }
}
