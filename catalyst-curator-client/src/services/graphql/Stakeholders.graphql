mutation createStakeholder($input: CreateStakeholderInput!) {
  createStakeholder(input: $input) {
    id
    name
    org
    firstName
    lastName
    title
    phone
    emailAddress
    altEmailAddress
    organizationRole
  }
}

query getStakeholder($id: String, $name: String, $org: String) {
  getStakeholder(name: $name, id: $id, org: $org) {
    id
    name
    org
    firstName
    lastName
    title
    phone
    emailAddress
    altEmailAddress
    organizationRole
  }
}

query queryStakeholders($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryStakeholders(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      name
      org
      firstName
      lastName
      title
      phone
      emailAddress
      altEmailAddress
      organizationRole
    }
  }
}

mutation updateStakeholder($id: String!, $input: UpdateStakeholderInput!) {
  updateStakeholder(id: $id, input: $input) {
    id
    name
    org
    firstName
    lastName
    title
    phone
    emailAddress
    altEmailAddress
    organizationRole
  }
}

mutation deleteStakeholder($id: String!) {
  deleteStakeholder(id: $id)
}
