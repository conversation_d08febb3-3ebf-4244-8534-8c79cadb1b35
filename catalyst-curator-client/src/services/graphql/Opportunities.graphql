mutation updateOpportunity($id: String!, $links: UpdateOpportunityLinks, $input: UpdateOpportunityInput!) {
  updateOpportunity(links: $links, id: $id, input: $input) {
    lastCurated
    curationInfo {
      lastCurated
      users {
        id
        firstName
        lastName
      }
    }
  }
}

query getOpportunity($id: String!) {
  getOpportunity(id: $id) {
    id
    createdAt
    lastCurated
    additionalNotes
    title
    statement
    context
    status
    function
    benefits
    solutionConcepts
    campaign
    armyModernizationPriority
    echelonApplicability
    operationalRules
    transitionInContactLineOfEffort
    isTiCLOE
    capabilityArea
    campaignNotes
    statusNotes
    priority
    priorityNotes
    solutionPathway
    solutionPathwayDetails
    permission
    attachmentNotes
    initiatives
    endorsements
    relatedOpportunityCount
    visibility
    DOTMLPFPPChange
    materielSolutionType
    feasibilitySummary
    existingArmyRequirement
    capabilitySponsor
    armyCapabilityManager
    opportunityOwnerStatuses {
      id
      createdAt
      status
      statusSetPreviousAt
      statusSetRemovedAt
      isRemoved
      owner {
        organizationRole
        user {
          id
          emailAddress
          firstName
          lastName
          org1
          org2
          org3
          org4
          phone
          altContact
        }
      }
    }
    submissions {
      id
      title
      statement
      context
      benefits
      solutionConcepts
      campaign
      user {
        id
        altContact
        createdAt
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        status
        updatedAt
      }
    }
    categories {
      id
      name
    }
    stakeholders {
      id
      name
      firstName
      lastName
      title
      phone
      emailAddress
      altEmailAddress
      org
      organizationRole
    }
    opportunities {
      id
      title
    }
    ownedOpportunities {
      id
      type
      target {
        id
        title
      }
    }
    owningOpportunities {
      id
      type
      source {
        id
        title
      }
    }
    user {
      id
      altContact
      createdAt
      emailAddress
      firstName
      lastName
      org1
      org2
      org3
      org4
      phone
      status
      updatedAt
    }
    attachments {
      id
      createdAt
      updatedAt
      name
      encoding
      mimetype
      displayName
      createdBy {
        firstName
        lastName
      }
      notes
    }
    links {
      id
      name
      url
      notes
      createdAt
      createdBy {
        firstName
        lastName
      }
    }
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
    projects {
      id
      title
    }
    existingSolutions {
      source
      title
      organization
      needsModification
      id
    }
    requirements {
      source
      title
      poc
      id
    }
  }
}

query getOpportunitySearchResult($id: String!) {
  getOpportunity(id: $id) {
    id
    status
    title
    ownedOpportunities {
      id
      type
      target {
        id
        title
      }
    }
    owningOpportunities {
      id
      type
      source {
        id
        title
      }
    }
  }
}

query getOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(searchSortInput: $searchSortInput, pagingInput: $pagingInput, scope: $scope) {
    results {
      id
      createdAt
      lastCurated
      additionalNotes
      title
      statement
      context
      status
      function
      armyModernizationPriority
      echelonApplicability
      opportunityOwnerStatuses {
        id
        createdAt
        status
        statusSetPreviousAt
        statusSetRemovedAt
        isRemoved
        owner {
          organizationRole
          user {
            id
            emailAddress
            firstName
            lastName
            org1
            org2
            org3
            org4
            phone
            altContact
          }
        }
      }
      operationalRules
      transitionInContactLineOfEffort
      isTiCLOE
      capabilityArea
      benefits
      solutionConcepts
      campaign
      statusNotes
      priority
      solutionPathway
      solutionPathwayDetails
      permission
      attachmentNotes
      visibility
      user {
        id
        altContact
        createdAt
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        status
        updatedAt
      }
      existingSolutions {
        source
        title
        organization
        needsModification
        id
      }
      requirements {
        source
        title
        poc
        id
      }
    }
  }
}

query getCurrentUser {
  getCurrentUser {
    id
    firstName
    lastName
    # opportunities {
    #     id
    #     title
    #     statement
    # }
  }
}

query calcOpportunities($calculation: Calculation!, $searchSortInput: SearchSortInput, $scope: Scope) {
  opportunityCalculation(calculation: $calculation, searchSortInput: $searchSortInput, scope: $scope) {
    operationResults {
      result
    }
  }
}

query getOpportunitiesReport($scope: Scope, $reportInput: ReportInput!) {
  report(scope: $scope, reportInput: $reportInput) {
    reports {
      name
      label
      data
    }
  }
}
