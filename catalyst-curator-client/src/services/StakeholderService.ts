import { OperationResult } from '@urql/core';
import {
  CreateStakeholderDocument,
  CreateStakeholderInput,
  DeleteStakeholderDocument,
  GetStakeholderDocument,
  QueryGetStakeholderArgs,
  QueryQueryStakeholdersArgs,
  QueryStakeholdersDocument,
  Stakeholder,
  UpdateStakeholderDocument,
  UpdateStakeholderInput,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class StakeholderService {
  getStakeholder(input: QueryGetStakeholderArgs): Promise<Stakeholder> {
    return urqlClient.executeQuery(GetStakeholderDocument, input).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<Stakeholder>((resolve, reject) => {
        if (data) {
          return resolve(data.getStakeholder);
        }
        return reject(error);
      });
    });
  }

  queryStakeholders(query: QueryQueryStakeholdersArgs): Promise<Array<Stakeholder>> {
    return urqlClient
      .executeQueryNoCache(QueryStakeholdersDocument, {
        pagingInput: query.pagingInput,
        searchSortInput: query.searchSortInput,
      })
      .then((results: OperationResult) => {
        const { data, error } = results;
        return new Promise<Array<Stakeholder>>((resolve, reject) => {
          if (data) {
            return resolve(data.queryStakeholders.results);
          }
          return reject(error);
        });
      });
  }

  createStakeholder(input: CreateStakeholderInput): Promise<Stakeholder> {
    return urqlClient.executeMutation(CreateStakeholderDocument, { input }).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<Stakeholder>((resolve, reject) => {
        if (data) {
          return resolve(data.createStakeholder);
        }
        return reject(error);
      });
    });
  }

  updateStakeholder(input: { id: string; input: UpdateStakeholderInput }): Promise<Stakeholder> {
    return urqlClient.executeMutation(UpdateStakeholderDocument, input).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<Stakeholder>((resolve, reject) => {
        if (data) {
          return resolve(data.updateStakeholder);
        }
        return reject(error);
      });
    });
  }

  deleteStakeholder(id: string): Promise<boolean> {
    return urqlClient.executeMutation(DeleteStakeholderDocument, { id }).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<boolean>((resolve, reject) => {
        if (data) {
          return resolve(data.deleteStakeholder);
        }
        return reject(error);
      });
    });
  }
}

export const stakeholderService = new StakeholderService();
