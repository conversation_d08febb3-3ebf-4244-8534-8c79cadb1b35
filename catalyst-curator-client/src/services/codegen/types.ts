import gql from 'graphql-tag';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  AnyScalar: any;
  DateTime: any;
  JSONObject: any;
  Upload: any;
};

export type AddLinkInput = {
  name: Scalars['String'];
  notes?: InputMaybe<Scalars['String']>;
  url: Scalars['String'];
};

export type ApplicationMeta = {
  __typename?: 'ApplicationMeta';
  createdAt: Scalars['DateTime'];
  curationMeta?: Maybe<Scalars['JSONObject']>;
  id: Scalars['ID'];
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type ApplicationMetaInput = {
  curationMeta?: InputMaybe<Scalars['JSONObject']>;
};

export type Attachment = {
  __typename?: 'Attachment';
  createdAt: Scalars['DateTime'];
  createdBy?: Maybe<User>;
  displayName?: Maybe<Scalars['String']>;
  encoding?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  mimetype?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  notes?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type AttachmentLinks = {
  opportunityId?: InputMaybe<Scalars['String']>;
  projectId?: InputMaybe<Scalars['String']>;
};

export type AuthResponse = {
  __typename?: 'AuthResponse';
  expiresAt: Scalars['DateTime'];
  token: Scalars['String'];
  user: User;
};

/** The type of query calculation */
export enum CalcOperator {
  Avg = 'AVG',
  Count = 'COUNT',
  Sum = 'SUM'
}

export type CalcResponse = {
  __typename?: 'CalcResponse';
  operationResults: Array<OperationResult>;
};

export type Calculation = {
  distinct?: InputMaybe<Scalars['Boolean']>;
  operations: Array<FieldOperations>;
};

export type Category = {
  __typename?: 'Category';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  opportunities: Array<Opportunity>;
  projects: Array<Project>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type CategoryPage = {
  __typename?: 'CategoryPage';
  pageInfo: PageInfo;
  results: Array<Category>;
};

export type CreateCategoryInput = {
  name: Scalars['String'];
};

export type CreateExistingSolutionInput = {
  needsModification?: InputMaybe<Scalars['Boolean']>;
  organization?: InputMaybe<Scalars['String']>;
  source: Scalars['String'];
  title?: InputMaybe<Scalars['String']>;
};

export type CreateOpportunityInput = {
  DOTMLPFPPChange?: InputMaybe<Array<Scalars['String']>>;
  additionalNotes?: InputMaybe<Scalars['String']>;
  armyCapabilityManager?: InputMaybe<Array<Scalars['String']>>;
  armyModernizationPriority?: InputMaybe<Scalars['String']>;
  attachmentNotes?: InputMaybe<Scalars['String']>;
  benefits?: InputMaybe<Scalars['String']>;
  campaign?: InputMaybe<Scalars['String']>;
  campaignNotes?: InputMaybe<Scalars['String']>;
  capabilityArea?: InputMaybe<Array<Scalars['String']>>;
  capabilitySponsor?: InputMaybe<Scalars['String']>;
  context: Scalars['String'];
  echelonApplicability?: InputMaybe<Scalars['String']>;
  endorsements?: InputMaybe<Scalars['String']>;
  existingArmyRequirement?: InputMaybe<Scalars['Boolean']>;
  feasibilitySummary?: InputMaybe<Scalars['String']>;
  function?: InputMaybe<Scalars['String']>;
  initiatives?: InputMaybe<Scalars['String']>;
  isTiCLOE?: InputMaybe<Scalars['Boolean']>;
  materielSolutionType?: InputMaybe<Scalars['String']>;
  operationalRules?: InputMaybe<Array<Scalars['String']>>;
  permission?: InputMaybe<Scalars['String']>;
  priority?: InputMaybe<Scalars['Int']>;
  priorityNotes?: InputMaybe<Scalars['String']>;
  solutionConcepts?: InputMaybe<Scalars['String']>;
  solutionPathway?: InputMaybe<Scalars['String']>;
  solutionPathwayDetails?: InputMaybe<Scalars['String']>;
  statement: Scalars['String'];
  status?: InputMaybe<OpportunityStatus>;
  statusNotes?: InputMaybe<Scalars['String']>;
  title: Scalars['String'];
  transitionInContactLineOfEffort?: InputMaybe<Scalars['String']>;
  visibility?: InputMaybe<OpportunityVisibility>;
};

export type CreateOpportunityLinks = {
  userId: Scalars['String'];
};

export type CreateOpportunityOwnerInput = {
  altContact?: InputMaybe<Scalars['String']>;
  emailAddress: Scalars['String'];
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  opportunityId?: InputMaybe<Scalars['String']>;
  org1?: InputMaybe<Scalars['String']>;
  org2?: InputMaybe<Scalars['String']>;
  org3?: InputMaybe<Scalars['String']>;
  org4?: InputMaybe<Scalars['String']>;
  organizationRole?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<OwnershipStatus>;
  statusSetPreviousAt?: InputMaybe<Scalars['DateTime']>;
  statusSetRemovedAt?: InputMaybe<Scalars['DateTime']>;
  userId?: InputMaybe<Scalars['String']>;
};

export type CreateOwnerInput = {
  altContact?: InputMaybe<Scalars['String']>;
  emailAddress: Scalars['String'];
  firstName: Scalars['String'];
  lastName: Scalars['String'];
  opportunityId?: InputMaybe<Scalars['String']>;
  org1?: InputMaybe<Scalars['String']>;
  org2?: InputMaybe<Scalars['String']>;
  org3?: InputMaybe<Scalars['String']>;
  org4?: InputMaybe<Scalars['String']>;
  organizationRole?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
};

export type CreatePrivilegeGroupInput = {
  name?: InputMaybe<Scalars['String']>;
};

export type CreateProjectFromOpportunityInput = {
  includeAttachments?: InputMaybe<Scalars['Boolean']>;
  includeCategories?: InputMaybe<Scalars['Boolean']>;
  includeProblemSolution?: InputMaybe<Scalars['Boolean']>;
  opportunityId: Scalars['String'];
  title: Scalars['String'];
};

export type CreateProjectInput = {
  background?: InputMaybe<Scalars['String']>;
  endDate?: InputMaybe<Scalars['DateTime']>;
  goals?: InputMaybe<Scalars['String']>;
  otherType?: InputMaybe<Scalars['String']>;
  startDate?: InputMaybe<Scalars['DateTime']>;
  status?: InputMaybe<ProjectStatus>;
  statusNotes?: InputMaybe<Scalars['String']>;
  summary?: InputMaybe<Scalars['String']>;
  title: Scalars['String'];
  type?: InputMaybe<Scalars['String']>;
};

export type CreateProjectLinks = {
  creatorId?: InputMaybe<Scalars['String']>;
};

export type CreateRequirementInput = {
  poc: Scalars['String'];
  source: Scalars['String'];
  title?: InputMaybe<Scalars['String']>;
};

export type CreateStakeholderInput = {
  altEmailAddress?: InputMaybe<Scalars['String']>;
  emailAddress?: InputMaybe<Scalars['String']>;
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  org?: InputMaybe<Scalars['String']>;
  organizationRole?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
};

export type CreateTenantAliasInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  content?: InputMaybe<Scalars['JSONObject']>;
  handle: Scalars['String'];
  name?: InputMaybe<Scalars['String']>;
  theme?: InputMaybe<Scalars['JSONObject']>;
};

export type CreateTenantInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  content?: InputMaybe<Scalars['JSONObject']>;
  handle: Scalars['String'];
  label?: InputMaybe<Scalars['String']>;
  name: Scalars['String'];
  theme?: InputMaybe<Scalars['JSONObject']>;
};

export type CreateUserInput = {
  altContact?: InputMaybe<Scalars['String']>;
  emailAddress: Scalars['String'];
  firstName: Scalars['String'];
  lastName: Scalars['String'];
  options?: InputMaybe<UserOptionsInput>;
  org1?: InputMaybe<Scalars['String']>;
  org2?: InputMaybe<Scalars['String']>;
  org3?: InputMaybe<Scalars['String']>;
  org4?: InputMaybe<Scalars['String']>;
  password: Scalars['String'];
  phone?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<VerifiedStatus>;
};

export type CurationEvent = {
  __typename?: 'CurationEvent';
  createdAt: Scalars['DateTime'];
  data?: Maybe<CurationEventData>;
  entityId?: Maybe<Scalars['String']>;
  entityType: EntityType;
  id: Scalars['ID'];
  type: CurationEventType;
  updatedAt?: Maybe<Scalars['DateTime']>;
  user?: Maybe<User>;
};

export type CurationEventData = {
  __typename?: 'CurationEventData';
  fields?: Maybe<Array<Scalars['String']>>;
};

export type CurationEventPage = {
  __typename?: 'CurationEventPage';
  pageInfo: PageInfo;
  results: Array<CurationEvent>;
};

/** The type of curation event */
export enum CurationEventType {
  Create = 'CREATE',
  Delete = 'DELETE',
  Update = 'UPDATE'
}

export type CurationInfo = {
  __typename?: 'CurationInfo';
  lastCurated?: Maybe<Scalars['DateTime']>;
  users: Array<User>;
};

/** The type of entity targeted */
export enum EntityType {
  Attachment = 'ATTACHMENT',
  Link = 'LINK',
  Opportunity = 'OPPORTUNITY',
  Project = 'PROJECT',
  User = 'USER'
}

export type ExistingSolution = {
  __typename?: 'ExistingSolution';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  needsModification: Scalars['Boolean'];
  opportunity: Opportunity;
  organization?: Maybe<Scalars['String']>;
  source: Scalars['String'];
  title?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type ExistingSolutionLinks = {
  opportunityId: Scalars['String'];
};

export type FieldOperations = {
  fieldName?: InputMaybe<Scalars['String']>;
  operator?: InputMaybe<CalcOperator>;
};

export type Link = {
  __typename?: 'Link';
  createdAt: Scalars['DateTime'];
  createdBy?: Maybe<User>;
  id: Scalars['ID'];
  name: Scalars['String'];
  notes?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  url: Scalars['String'];
};

export type LinkLinks = {
  opportunityId?: InputMaybe<Scalars['String']>;
  projectId?: InputMaybe<Scalars['String']>;
};

export type Location = {
  __typename?: 'Location';
  location: Scalars['String'];
};

export type Mutation = {
  __typename?: 'Mutation';
  addAttachment: Attachment;
  addLink: Link;
  /** Create a new ApplicationMeta */
  createApplicationMeta: ApplicationMeta;
  /** Create a new Category */
  createCategory: Category;
  /** Create a new ExistingSolution */
  createExistingSolution: ExistingSolution;
  /** Create a new Opportunity */
  createOpportunity: Opportunity;
  /** Create a new Owner for opportunity */
  createOpportunityOwner: OpportunityOwnerStatus;
  /** Create a new Owner for opportunity */
  createOwner: Owner;
  /** Create a new PrivilegeGroup */
  createPrivilegeGroup: PrivilegeGroup;
  /** Create a new Project */
  createProject: Project;
  /** Create a new Project from an Opportunity */
  createProjectFromOpportunity: Project;
  /** Create a new Requirement */
  createRequirement: Requirement;
  /** Create a new Stakeholder */
  createStakeholder: Stakeholder;
  /** Create a new Tenant */
  createTenant: Tenant;
  /** Create a new TenantAlias */
  createTenantAlias: TenantAlias;
  /** (Admin) Create a new User */
  createUser: User;
  /** Delete an existing ApplicationMeta */
  deleteApplicationMeta: Scalars['Boolean'];
  /** Delete the Attachment */
  deleteAttachment: Scalars['Boolean'];
  /** Delete an existing Category */
  deleteCategory: Scalars['Boolean'];
  /** Delete an existing CurationEvent */
  deleteCurationEvent: Scalars['Boolean'];
  /** Delete the current User account */
  deleteCurrentUser: Scalars['Boolean'];
  /** Delete an existing ExistingSolution */
  deleteExistingSolution: Scalars['Boolean'];
  /** Delete the Link */
  deleteLink: Scalars['Boolean'];
  /** Delete an existing Opportunity */
  deleteOpportunity: Scalars['Boolean'];
  /** Delete an existing PrivilegeGroup */
  deletePrivilegeGroup: Scalars['Boolean'];
  /** Delete an existing Project */
  deleteProject: Scalars['Boolean'];
  /** Delete an existing Requirement */
  deleteRequirement: Scalars['Boolean'];
  /** Delete an existing Stakeholder */
  deleteStakeholder: Scalars['Boolean'];
  /** Delete an existing Tenant */
  deleteTenant: Scalars['Boolean'];
  /** Delete an existing TenantAlias */
  deleteTenantAlias: Scalars['Boolean'];
  /** (Admin) Delete an existing User */
  deleteUser: Scalars['Boolean'];
  /** Authenticate a User and receive a token */
  login: AuthResponse;
  /** (Unauthenticated) Register a new user with a password */
  register: AuthResponse;
  /** Authenticate a User and receive a token */
  renew: AuthResponse;
  /** Update an existing ApplicationMeta */
  updateApplicationMeta: ApplicationMeta;
  /** Update an existing Attachment */
  updateAttachment: Attachment;
  /** Update an existing Category */
  updateCategory: Category;
  /** Update the current User account */
  updateCurrentUser: User;
  /** Update an existing ExistingSolution */
  updateExistingSolution: ExistingSolution;
  /** Update an existing Link */
  updateLink: Link;
  /** Update an existing Opportunity */
  updateOpportunity: Opportunity;
  /** Update an existing Owner */
  updateOpportunityOwner: OpportunityOwnerStatus;
  /** Update an existing Owner */
  updateOwner: Owner;
  /** Update an existing PrivilegeGroup */
  updatePrivilegeGroup: PrivilegeGroup;
  /** Update an existing Project */
  updateProject: Project;
  /** Update an existing Requirement */
  updateRequirement: Requirement;
  /** Update an existing Stakeholder */
  updateStakeholder: Stakeholder;
  /** Update an existing Tenant */
  updateTenant: Tenant;
  /** Update an existing TenantAlias */
  updateTenantAlias: TenantAlias;
  /** (Admin) Update an existing User */
  updateUser: User;
};


export type MutationAddAttachmentArgs = {
  input: Scalars['Upload'];
  links: AttachmentLinks;
};


export type MutationAddLinkArgs = {
  input: AddLinkInput;
  links: LinkLinks;
};


export type MutationCreateApplicationMetaArgs = {
  input: ApplicationMetaInput;
};


export type MutationCreateCategoryArgs = {
  input: CreateCategoryInput;
};


export type MutationCreateExistingSolutionArgs = {
  input: CreateExistingSolutionInput;
  links: ExistingSolutionLinks;
};


export type MutationCreateOpportunityArgs = {
  input: CreateOpportunityInput;
  links: CreateOpportunityLinks;
};


export type MutationCreateOpportunityOwnerArgs = {
  input: CreateOpportunityOwnerInput;
  links?: InputMaybe<OpportunityOwnerLinks>;
};


export type MutationCreateOwnerArgs = {
  input: CreateOwnerInput;
  links?: InputMaybe<OwnerLinks>;
};


export type MutationCreatePrivilegeGroupArgs = {
  input: CreatePrivilegeGroupInput;
};


export type MutationCreateProjectArgs = {
  input: CreateProjectInput;
  links?: InputMaybe<CreateProjectLinks>;
};


export type MutationCreateProjectFromOpportunityArgs = {
  input: CreateProjectFromOpportunityInput;
};


export type MutationCreateRequirementArgs = {
  input: CreateRequirementInput;
  links: RequirementLinks;
};


export type MutationCreateStakeholderArgs = {
  input: CreateStakeholderInput;
};


export type MutationCreateTenantArgs = {
  adminPass: Scalars['String'];
  input: CreateTenantInput;
};


export type MutationCreateTenantAliasArgs = {
  input: CreateTenantAliasInput;
};


export type MutationCreateUserArgs = {
  input: CreateUserInput;
  links?: InputMaybe<UserLinks>;
};


export type MutationDeleteApplicationMetaArgs = {
  id: Scalars['String'];
};


export type MutationDeleteAttachmentArgs = {
  id: Scalars['String'];
};


export type MutationDeleteCategoryArgs = {
  id: Scalars['String'];
};


export type MutationDeleteCurationEventArgs = {
  id: Scalars['String'];
};


export type MutationDeleteExistingSolutionArgs = {
  id: Scalars['String'];
};


export type MutationDeleteLinkArgs = {
  id: Scalars['String'];
};


export type MutationDeleteOpportunityArgs = {
  id: Scalars['String'];
};


export type MutationDeletePrivilegeGroupArgs = {
  id: Scalars['String'];
};


export type MutationDeleteProjectArgs = {
  id: Scalars['String'];
};


export type MutationDeleteRequirementArgs = {
  id: Scalars['String'];
};


export type MutationDeleteStakeholderArgs = {
  id: Scalars['String'];
};


export type MutationDeleteTenantArgs = {
  id: Scalars['String'];
};


export type MutationDeleteTenantAliasArgs = {
  id: Scalars['String'];
};


export type MutationDeleteUserArgs = {
  id: Scalars['String'];
};


export type MutationLoginArgs = {
  password: Scalars['String'];
  tenantHandle: Scalars['String'];
  userName: Scalars['String'];
};


export type MutationRegisterArgs = {
  input: RegisterUserInput;
};


export type MutationUpdateApplicationMetaArgs = {
  id: Scalars['String'];
  input: ApplicationMetaInput;
};


export type MutationUpdateAttachmentArgs = {
  input: UpdateAttachmentInput;
};


export type MutationUpdateCategoryArgs = {
  id: Scalars['String'];
  input: UpdateCategoryInput;
};


export type MutationUpdateCurrentUserArgs = {
  input: UpdateCurrentUserInput;
  links?: InputMaybe<UpdateCurrentUserLinks>;
};


export type MutationUpdateExistingSolutionArgs = {
  id: Scalars['String'];
  input: UpdateExistingSolutionInput;
};


export type MutationUpdateLinkArgs = {
  input: UpdateLinkInput;
};


export type MutationUpdateOpportunityArgs = {
  id: Scalars['String'];
  input: UpdateOpportunityInput;
  links?: InputMaybe<UpdateOpportunityLinks>;
};


export type MutationUpdateOpportunityOwnerArgs = {
  id: Scalars['String'];
  input: UpdateOpportunityOwnerInput;
  links?: InputMaybe<OpportunityOwnerLinks>;
};


export type MutationUpdateOwnerArgs = {
  id: Scalars['String'];
  input: UpdateOwnerInput;
  links?: InputMaybe<OwnerLinks>;
};


export type MutationUpdatePrivilegeGroupArgs = {
  id: Scalars['String'];
  input: UpdatePrivilegeGroupInput;
  links?: InputMaybe<UpdatePrivilegeGroupLinks>;
};


export type MutationUpdateProjectArgs = {
  id: Scalars['String'];
  input: UpdateProjectInput;
  links?: InputMaybe<UpdateProjectLinks>;
};


export type MutationUpdateRequirementArgs = {
  id: Scalars['String'];
  input: UpdateRequirementInput;
};


export type MutationUpdateStakeholderArgs = {
  id: Scalars['String'];
  input: UpdateStakeholderInput;
};


export type MutationUpdateTenantArgs = {
  id: Scalars['String'];
  input: UpdateTenantInput;
};


export type MutationUpdateTenantAliasArgs = {
  id: Scalars['String'];
  input: UpdateTenantAliasInput;
};


export type MutationUpdateUserArgs = {
  id: Scalars['String'];
  input: UpdateUserInput;
  links?: InputMaybe<UserLinks>;
};

export type OperationResult = {
  __typename?: 'OperationResult';
  result?: Maybe<Scalars['AnyScalar']>;
};

export type Opportunity = {
  __typename?: 'Opportunity';
  DOTMLPFPPChange?: Maybe<Array<Scalars['String']>>;
  additionalNotes?: Maybe<Scalars['String']>;
  armyCapabilityManager?: Maybe<Array<Scalars['String']>>;
  armyModernizationPriority?: Maybe<Scalars['String']>;
  attachmentNotes?: Maybe<Scalars['String']>;
  attachments: Array<Attachment>;
  benefits?: Maybe<Scalars['String']>;
  campaign?: Maybe<Scalars['String']>;
  campaignNotes?: Maybe<Scalars['String']>;
  capabilityArea?: Maybe<Array<Scalars['String']>>;
  capabilitySponsor?: Maybe<Scalars['String']>;
  categories: Array<Category>;
  childOpportunityCount?: Maybe<Scalars['Int']>;
  context: Scalars['String'];
  createdAt: Scalars['DateTime'];
  curationInfo?: Maybe<CurationInfo>;
  echelonApplicability?: Maybe<Scalars['String']>;
  endorsements?: Maybe<Scalars['String']>;
  existingArmyRequirement: Scalars['Boolean'];
  existingSolutions: Array<ExistingSolution>;
  feasibilitySummary?: Maybe<Scalars['String']>;
  function?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  initiatives?: Maybe<Scalars['String']>;
  isTiCLOE: Scalars['Boolean'];
  lastCurated?: Maybe<Scalars['DateTime']>;
  linkedOpportunityCount?: Maybe<Scalars['Int']>;
  links: Array<Link>;
  materielSolutionType?: Maybe<Scalars['String']>;
  operationalRules?: Maybe<Array<Scalars['String']>>;
  opportunities: Array<Opportunity>;
  opportunityOwnerStatuses: Array<OpportunityOwnerStatus>;
  org1?: Maybe<Scalars['String']>;
  org2?: Maybe<Scalars['String']>;
  org3?: Maybe<Scalars['String']>;
  org4?: Maybe<Scalars['String']>;
  ownedOpportunities: Array<RelatedOpportunity>;
  owningOpportunities: Array<RelatedOpportunity>;
  parentOpportunityCount?: Maybe<Scalars['Int']>;
  permission?: Maybe<Scalars['String']>;
  priority?: Maybe<Scalars['Int']>;
  priorityNotes?: Maybe<Scalars['String']>;
  projects: Array<Project>;
  relatedOpportunityCount?: Maybe<Scalars['Int']>;
  requirements: Array<Requirement>;
  solutionConcepts?: Maybe<Scalars['String']>;
  solutionPathway?: Maybe<Scalars['String']>;
  solutionPathwayDetails?: Maybe<Scalars['String']>;
  stakeholders: Array<Stakeholder>;
  statement: Scalars['String'];
  status: OpportunityStatus;
  statusNotes?: Maybe<Scalars['String']>;
  submissions: Array<Submission>;
  tenant?: Maybe<Tenant>;
  title: Scalars['String'];
  transitionInContactLineOfEffort?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  user?: Maybe<User>;
  visibility: OpportunityVisibility;
};

export type OpportunityOwnerLinks = {
  opportunity?: InputMaybe<Array<UpdateLinks>>;
  owner?: InputMaybe<Array<UpdateLinks>>;
};

export type OpportunityOwnerPage = {
  __typename?: 'OpportunityOwnerPage';
  pageInfo: PageInfo;
  results: Array<OpportunityOwnerStatus>;
};

export type OpportunityOwnerStatus = {
  __typename?: 'OpportunityOwnerStatus';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  isRemoved: Scalars['Boolean'];
  opportunity: Opportunity;
  owner?: Maybe<Owner>;
  status: OwnershipStatus;
  statusSetPreviousAt?: Maybe<Scalars['DateTime']>;
  statusSetRemovedAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type OpportunityPage = {
  __typename?: 'OpportunityPage';
  pageInfo: PageInfo;
  results: Array<Opportunity>;
};

/** Status of the Opportunity */
export enum OpportunityStatus {
  Approved = 'APPROVED',
  Archived = 'ARCHIVED',
  Deleted = 'DELETED',
  Pending = 'PENDING'
}

/** Sets the visibility status of an opportunity */
export enum OpportunityVisibility {
  All = 'ALL',
  Private = 'PRIVATE'
}

export type Owner = {
  __typename?: 'Owner';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  opportunityOwnerStatuses?: Maybe<Array<OpportunityOwnerStatus>>;
  organizationRole?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  user: User;
};

export type OwnerLinks = {
  opportunities?: InputMaybe<Array<UpdateLinks>>;
  user?: InputMaybe<Array<UpdateLinks>>;
};

export type OwnerPage = {
  __typename?: 'OwnerPage';
  pageInfo: PageInfo;
  results: Array<Owner>;
};

/** The status of the owner as it applies to an opportunity */
export enum OwnershipStatus {
  Current = 'CURRENT',
  Initial = 'INITIAL',
  Previous = 'PREVIOUS',
  Removed = 'REMOVED'
}

export type PageInfo = {
  __typename?: 'PageInfo';
  hasNext: Scalars['Boolean'];
  hasPrevious: Scalars['Boolean'];
  lastCursor: Scalars['String'];
  lastPageSize: Scalars['Int'];
  retrievedCount: Scalars['Int'];
  totalCount: Scalars['Int'];
};

/** Controls list paging */
export type PagingInput = {
  /** How many results to skip or the 'zero-based' starting index */
  cursor?: InputMaybe<Scalars['String']>;
  /** The number of rows to request, per page.  Defaults to 100. Max 500 */
  pageSize?: InputMaybe<Scalars['Int']>;
};

export type Privilege = {
  __typename?: 'Privilege';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  resourceId?: Maybe<Scalars['String']>;
  resourceType: ResourceType;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type PrivilegeGroup = {
  __typename?: 'PrivilegeGroup';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  privileges?: Maybe<Array<Privilege>>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  users?: Maybe<Array<User>>;
};

export type Project = {
  __typename?: 'Project';
  attachments: Array<Attachment>;
  background?: Maybe<Scalars['String']>;
  categories: Array<Category>;
  createdAt: Scalars['DateTime'];
  creator?: Maybe<User>;
  curationInfo?: Maybe<CurationInfo>;
  endDate?: Maybe<Scalars['DateTime']>;
  goals?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  lastCurated?: Maybe<Scalars['DateTime']>;
  opportunities: Array<Opportunity>;
  otherType?: Maybe<Scalars['String']>;
  projectStakeholders: Array<ProjectStakeholder>;
  startDate?: Maybe<Scalars['DateTime']>;
  status: ProjectStatus;
  statusNotes?: Maybe<Scalars['String']>;
  summary?: Maybe<Scalars['String']>;
  tenant?: Maybe<Tenant>;
  title: Scalars['String'];
  type?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type ProjectPage = {
  __typename?: 'ProjectPage';
  pageInfo: PageInfo;
  results: Array<Project>;
};

export type ProjectStakeHolderUpdateItem = {
  id: Scalars['String'];
  type: ProjectStakeholderType;
};

export type ProjectStakeHolderUpdateLinks = {
  items: Array<ProjectStakeHolderUpdateItem>;
  operator: UpdateOperator;
};

export type ProjectStakeholder = {
  __typename?: 'ProjectStakeholder';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  project: Project;
  stakeholder: Stakeholder;
  type: ProjectStakeholderType;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

/** The type of project stakeholder */
export enum ProjectStakeholderType {
  Division = 'DIVISION',
  Performer = 'PERFORMER',
  Transition = 'TRANSITION'
}

/** The status of the project */
export enum ProjectStatus {
  Active = 'ACTIVE',
  Archived = 'ARCHIVED',
  Completed = 'COMPLETED',
  Deleted = 'DELETED',
  Pending = 'PENDING'
}

export type Query = {
  __typename?: 'Query';
  /** Create a report */
  calculation: CalcResponse;
  /** Returns matching Tenants and Tenant Aliases */
  findTenantInfoByName: Array<TenantInfo>;
  /** Get the ApplicationMeta */
  getApplicationMeta?: Maybe<ApplicationMeta>;
  /** Get the Attachment Location */
  getAttachmentLocation: Location;
  /** Get the Category */
  getCategory?: Maybe<Category>;
  /** Get the CurationEvent */
  getCurationEvent?: Maybe<CurationEvent>;
  /** Get the current User account */
  getCurrentUser?: Maybe<User>;
  /** Get an ExistingSolution by ID */
  getExistingSolution?: Maybe<ExistingSolution>;
  /** Get all ExistingSolutions for an Opportunity */
  getExistingSolutionsByOpportunity: Array<ExistingSolution>;
  /**
   * Get all Opportunities
   * @deprecated User queryOpportunities() instead
   */
  getOpportunities: Array<Opportunity>;
  /** Get the Opportunity */
  getOpportunity?: Maybe<Opportunity>;
  /** Retrieve an Owner by its owner id */
  getOpportunityOwner?: Maybe<OpportunityOwnerStatus>;
  /** Retrieve an Owner */
  getOwner?: Maybe<Owner>;
  /** Get the PrivilegeGroup */
  getPrivilegeGroup?: Maybe<PrivilegeGroup>;
  /** Get the Project */
  getProject?: Maybe<Project>;
  /** Get a Requirement by ID */
  getRequirement?: Maybe<Requirement>;
  /** Get all Requirements for an Opportunity */
  getRequirementsByOpportunity: Array<Requirement>;
  /** Get the Stakeholder */
  getStakeholder?: Maybe<Stakeholder>;
  /** Get the Tenant */
  getTenant?: Maybe<Tenant>;
  /** Get the TenantAlias */
  getTenantAlias?: Maybe<TenantAlias>;
  /** Get the TenantInfo */
  getTenantInfo?: Maybe<TenantInfo>;
  /** Retrieve a User */
  getUser?: Maybe<User>;
  /**
   * Retrieves Users for this Tenant
   * @deprecated User queryUsers() instead
   */
  getUsers: Array<User>;
  /** Perform a calculation on Opportunities */
  opportunityCalculation: CalcResponse;
  /** Get a page of matching Categories */
  queryCategories: CategoryPage;
  /** Get a page of matching CurationEvents */
  queryCurationEvents: CurationEventPage;
  /** Get a page of matching Opportunities */
  queryOpportunities: OpportunityPage;
  /** Get a page of matching Owners */
  queryOpportunityOwners: OpportunityOwnerPage;
  /** Get a page of matching Owners */
  queryOwners: OwnerPage;
  /** Get a page of matching Projects */
  queryProjects: ProjectPage;
  /** Get a page of matching Stakeholders */
  queryStakeholders: StakeholderPage;
  /** Get a page of matching TenantAliases */
  queryTenantAliases: TenantAliasPage;
  /** Get a page of matching Tenants */
  queryTenants: TenantPage;
  /** Get a page of matching Users */
  queryUsers: UserPage;
  /** Create a report */
  report: ReportResponse;
};


export type QueryCalculationArgs = {
  calculation: Calculation;
  entityName: Scalars['String'];
  scope?: InputMaybe<Scope>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryFindTenantInfoByNameArgs = {
  name: Scalars['String'];
};


export type QueryGetApplicationMetaArgs = {
  id: Scalars['String'];
};


export type QueryGetAttachmentLocationArgs = {
  id: Scalars['String'];
};


export type QueryGetCategoryArgs = {
  id?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
};


export type QueryGetCurationEventArgs = {
  id: Scalars['String'];
};


export type QueryGetExistingSolutionArgs = {
  id: Scalars['String'];
};


export type QueryGetExistingSolutionsByOpportunityArgs = {
  opportunityId: Scalars['String'];
};


export type QueryGetOpportunitiesArgs = {
  scope?: InputMaybe<Scope>;
};


export type QueryGetOpportunityArgs = {
  id: Scalars['String'];
};


export type QueryGetOpportunityOwnerArgs = {
  id: Scalars['String'];
};


export type QueryGetOwnerArgs = {
  id: Scalars['String'];
  scope?: InputMaybe<Scope>;
};


export type QueryGetPrivilegeGroupArgs = {
  id?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
};


export type QueryGetProjectArgs = {
  id: Scalars['String'];
};


export type QueryGetRequirementArgs = {
  id: Scalars['String'];
};


export type QueryGetRequirementsByOpportunityArgs = {
  opportunityId: Scalars['String'];
};


export type QueryGetStakeholderArgs = {
  firstName?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  org?: InputMaybe<Scalars['String']>;
};


export type QueryGetTenantArgs = {
  handleOrAlias?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['String']>;
};


export type QueryGetTenantAliasArgs = {
  handle?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['String']>;
};


export type QueryGetTenantInfoArgs = {
  handleOrAlias?: InputMaybe<Scalars['String']>;
};


export type QueryGetUserArgs = {
  id: Scalars['String'];
};


export type QueryOpportunityCalculationArgs = {
  calculation: Calculation;
  scope?: InputMaybe<Scope>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryCategoriesArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryCurationEventsArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryOpportunitiesArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  scope?: InputMaybe<Scope>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryOpportunityOwnersArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  scope?: InputMaybe<Scope>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryOwnersArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  scope?: InputMaybe<Scope>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryProjectsArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryStakeholdersArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryTenantAliasesArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryTenantsArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryQueryUsersArgs = {
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
};


export type QueryReportArgs = {
  reportInput: ReportInput;
  scope?: InputMaybe<Scope>;
};

export type RegisterUserInput = {
  altContact?: InputMaybe<Scalars['String']>;
  emailAddress: Scalars['String'];
  firstName: Scalars['String'];
  lastName: Scalars['String'];
  options?: InputMaybe<UserOptionsInput>;
  org1?: InputMaybe<Scalars['String']>;
  org2?: InputMaybe<Scalars['String']>;
  org3?: InputMaybe<Scalars['String']>;
  org4?: InputMaybe<Scalars['String']>;
  password: Scalars['String'];
  phone?: InputMaybe<Scalars['String']>;
  tenantHandle: Scalars['String'];
};

export type RelatedOpportunity = {
  __typename?: 'RelatedOpportunity';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  source: Opportunity;
  target: Opportunity;
  type: RelatedOpportunityType;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

/** The type of related opportunity */
export enum RelatedOpportunityType {
  Child = 'CHILD',
  Linked = 'LINKED'
}

export type RelatedOpportunityUpdateItem = {
  id: Scalars['String'];
  type: RelatedOpportunityType;
};

export type RelatedOpportunityUpdateLinks = {
  items: Array<RelatedOpportunityUpdateItem>;
  operator: UpdateOperator;
};

export type Report = {
  __typename?: 'Report';
  data?: Maybe<Scalars['JSONObject']>;
  label: Scalars['String'];
  name: Scalars['String'];
};

export type ReportInput = {
  queries: Array<ReportQuery>;
};

export type ReportQuery = {
  label?: InputMaybe<Scalars['String']>;
  reportName: Scalars['String'];
  searchSortInput?: InputMaybe<SearchSortInput>;
};

export type ReportResponse = {
  __typename?: 'ReportResponse';
  reports: Array<Report>;
};

export type Requirement = {
  __typename?: 'Requirement';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  opportunity: Opportunity;
  poc: Scalars['String'];
  source: Scalars['String'];
  title?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type RequirementLinks = {
  opportunityId: Scalars['String'];
};

export type Resource = {
  resourceId: Scalars['String'];
  resourceType: ResourceType;
};

/** The type of resource targeted */
export enum ResourceType {
  Tenant = 'TENANT'
}

export type Role = {
  __typename?: 'Role';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: RoleNames;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

/** User role */
export enum RoleNames {
  Admin = 'ADMIN',
  Analyst = 'ANALYST',
  Curator = 'CURATOR',
  Owner = 'OWNER'
}

export type Scope = {
  resources: Array<Resource>;
};

export type SearchField = {
  fieldNames: Array<Scalars['String']>;
  operator?: InputMaybe<SearchOperator>;
  searchValue?: InputMaybe<Scalars['AnyScalar']>;
};

/** The search operation to apply to a field value */
export enum SearchOperator {
  Eq = 'EQ',
  Gt = 'GT',
  Gte = 'GTE',
  In = 'IN',
  Lt = 'LT',
  Lte = 'LTE',
  Match = 'MATCH',
  Ne = 'NE',
  Nin = 'NIN'
}

export type SearchSortInput = {
  jsonSearchGroups?: InputMaybe<Array<Scalars['JSONObject']>>;
  searchFields?: InputMaybe<Array<SearchField>>;
  sortFields?: InputMaybe<Array<SortField>>;
};

export type SortField = {
  ascending?: InputMaybe<Scalars['Boolean']>;
  fieldName: Scalars['String'];
};

export type Stakeholder = {
  __typename?: 'Stakeholder';
  altEmailAddress?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  emailAddress?: Maybe<Scalars['String']>;
  firstName?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  lastName?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  opportunities: Array<Opportunity>;
  org?: Maybe<Scalars['String']>;
  organizationRole?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  title?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type StakeholderPage = {
  __typename?: 'StakeholderPage';
  pageInfo: PageInfo;
  results: Array<Stakeholder>;
};

export type Submission = {
  __typename?: 'Submission';
  benefits?: Maybe<Scalars['String']>;
  campaign?: Maybe<Scalars['String']>;
  context: Scalars['String'];
  createdAt: Scalars['DateTime'];
  function?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  opportunities: Array<Opportunity>;
  solutionConcepts?: Maybe<Scalars['String']>;
  statement: Scalars['String'];
  title: Scalars['String'];
  updatedAt?: Maybe<Scalars['DateTime']>;
  user?: Maybe<User>;
};

export type Tenant = {
  __typename?: 'Tenant';
  createdAt: Scalars['DateTime'];
  handle: Scalars['String'];
  id: Scalars['ID'];
  label?: Maybe<Scalars['String']>;
  meta?: Maybe<TenantMeta>;
  name: Scalars['String'];
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TenantAlias = {
  __typename?: 'TenantAlias';
  createdAt: Scalars['DateTime'];
  handle: Scalars['String'];
  id: Scalars['ID'];
  label?: Maybe<Scalars['String']>;
  meta?: Maybe<TenantMeta>;
  name?: Maybe<Scalars['String']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TenantAliasPage = {
  __typename?: 'TenantAliasPage';
  pageInfo: PageInfo;
  results: Array<TenantAlias>;
};

export type TenantInfo = {
  __typename?: 'TenantInfo';
  handle: Scalars['String'];
  label?: Maybe<Scalars['String']>;
  meta?: Maybe<TenantMeta>;
  name: Scalars['String'];
  serverVersion: Scalars['String'];
  tenantId: Scalars['String'];
};

export type TenantMeta = {
  __typename?: 'TenantMeta';
  config?: Maybe<Scalars['JSONObject']>;
  content?: Maybe<Scalars['JSONObject']>;
  createdAt: Scalars['DateTime'];
  filterOtherPrivateOpportunities: Scalars['Boolean'];
  id: Scalars['ID'];
  serverConfiguration?: Maybe<Scalars['JSONObject']>;
  theme?: Maybe<Scalars['JSONObject']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type TenantPage = {
  __typename?: 'TenantPage';
  pageInfo: PageInfo;
  results: Array<Tenant>;
};

export type UpdateAttachmentInput = {
  displayName?: InputMaybe<Scalars['String']>;
  id: Scalars['String'];
  notes?: InputMaybe<Scalars['String']>;
};

export type UpdateCategoryInput = {
  name: Scalars['String'];
};

export type UpdateCurrentUserInput = {
  altContact?: InputMaybe<Scalars['String']>;
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  options?: InputMaybe<UserOptionsInput>;
  org1?: InputMaybe<Scalars['String']>;
  org2?: InputMaybe<Scalars['String']>;
  org3?: InputMaybe<Scalars['String']>;
  org4?: InputMaybe<Scalars['String']>;
  password?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
};

export type UpdateCurrentUserLinks = {
  appMetaId?: InputMaybe<Scalars['String']>;
};

export type UpdateExistingSolutionInput = {
  needsModification?: InputMaybe<Scalars['Boolean']>;
  organization?: InputMaybe<Scalars['String']>;
  source?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
};

export type UpdateLinkInput = {
  id: Scalars['String'];
  name?: InputMaybe<Scalars['String']>;
  notes?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
};

export type UpdateLinks = {
  ids: Array<Scalars['String']>;
  operator: UpdateOperator;
};

/** The update operation to apply to a field value */
export enum UpdateOperator {
  Add = 'ADD',
  Rm = 'RM',
  Set = 'SET'
}

export type UpdateOpportunityInput = {
  DOTMLPFPPChange?: InputMaybe<Array<Scalars['String']>>;
  additionalNotes?: InputMaybe<Scalars['String']>;
  armyCapabilityManager?: InputMaybe<Array<Scalars['String']>>;
  armyModernizationPriority?: InputMaybe<Scalars['String']>;
  attachmentNotes?: InputMaybe<Scalars['String']>;
  benefits?: InputMaybe<Scalars['String']>;
  campaign?: InputMaybe<Scalars['String']>;
  campaignNotes?: InputMaybe<Scalars['String']>;
  capabilityArea?: InputMaybe<Array<Scalars['String']>>;
  capabilitySponsor?: InputMaybe<Scalars['String']>;
  context?: InputMaybe<Scalars['String']>;
  echelonApplicability?: InputMaybe<Scalars['String']>;
  endorsements?: InputMaybe<Scalars['String']>;
  existingArmyRequirement?: InputMaybe<Scalars['Boolean']>;
  feasibilitySummary?: InputMaybe<Scalars['String']>;
  function?: InputMaybe<Scalars['String']>;
  initiatives?: InputMaybe<Scalars['String']>;
  isTiCLOE?: InputMaybe<Scalars['Boolean']>;
  materielSolutionType?: InputMaybe<Scalars['String']>;
  operationalRules?: InputMaybe<Array<Scalars['String']>>;
  permission?: InputMaybe<Scalars['String']>;
  priority?: InputMaybe<Scalars['Int']>;
  priorityNotes?: InputMaybe<Scalars['String']>;
  solutionConcepts?: InputMaybe<Scalars['String']>;
  solutionPathway?: InputMaybe<Scalars['String']>;
  solutionPathwayDetails?: InputMaybe<Scalars['String']>;
  statement?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<OpportunityStatus>;
  statusNotes?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
  transitionInContactLineOfEffort?: InputMaybe<Scalars['String']>;
  visibility?: InputMaybe<OpportunityVisibility>;
};

export type UpdateOpportunityLinks = {
  categories?: InputMaybe<Array<UpdateLinks>>;
  opportunities?: InputMaybe<Array<UpdateLinks>>;
  relatedOpportunities?: InputMaybe<Array<RelatedOpportunityUpdateLinks>>;
  stakeholders?: InputMaybe<Array<UpdateLinks>>;
  userId?: InputMaybe<Scalars['String']>;
};

export type UpdateOpportunityOwnerInput = {
  organizationRole?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<OwnershipStatus>;
  statusSetPreviousAt?: InputMaybe<Scalars['DateTime']>;
  statusSetRemovedAt?: InputMaybe<Scalars['DateTime']>;
};

export type UpdateOwnerInput = {
  organizationRole?: InputMaybe<Scalars['String']>;
};

export type UpdatePrivilegeGroupInput = {
  name?: InputMaybe<Scalars['String']>;
};

export type UpdatePrivilegeGroupLinks = {
  privileges?: InputMaybe<Array<UpdateLinks>>;
};

export type UpdateProjectInput = {
  background?: InputMaybe<Scalars['String']>;
  endDate?: InputMaybe<Scalars['DateTime']>;
  goals?: InputMaybe<Scalars['String']>;
  otherType?: InputMaybe<Scalars['String']>;
  startDate?: InputMaybe<Scalars['DateTime']>;
  status?: InputMaybe<ProjectStatus>;
  statusNotes?: InputMaybe<Scalars['String']>;
  summary?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['String']>;
};

export type UpdateProjectLinks = {
  categories?: InputMaybe<Array<UpdateLinks>>;
  creatorId?: InputMaybe<Scalars['String']>;
  opportunities?: InputMaybe<Array<UpdateLinks>>;
  projectStakeholders?: InputMaybe<Array<ProjectStakeHolderUpdateLinks>>;
};

export type UpdateRequirementInput = {
  poc?: InputMaybe<Scalars['String']>;
  source?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
};

export type UpdateStakeholderInput = {
  altEmailAddress?: InputMaybe<Scalars['String']>;
  emailAddress?: InputMaybe<Scalars['String']>;
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  org?: InputMaybe<Scalars['String']>;
  organizationRole?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
};

export type UpdateTenantAliasInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  content?: InputMaybe<Scalars['JSONObject']>;
  handle?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  theme?: InputMaybe<Scalars['JSONObject']>;
};

export type UpdateTenantInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  content?: InputMaybe<Scalars['JSONObject']>;
  handle?: InputMaybe<Scalars['String']>;
  label?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  theme?: InputMaybe<Scalars['JSONObject']>;
};

export type UpdateUserInput = {
  altContact?: InputMaybe<Scalars['String']>;
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  options?: InputMaybe<UserOptionsInput>;
  org1?: InputMaybe<Scalars['String']>;
  org2?: InputMaybe<Scalars['String']>;
  org3?: InputMaybe<Scalars['String']>;
  org4?: InputMaybe<Scalars['String']>;
  password?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<VerifiedStatus>;
};

export type User = {
  __typename?: 'User';
  altContact?: Maybe<Scalars['String']>;
  appMeta?: Maybe<ApplicationMeta>;
  createdAt: Scalars['DateTime'];
  curationEvent: Array<CurationEvent>;
  emailAddress: Scalars['String'];
  firstName: Scalars['String'];
  id: Scalars['ID'];
  lastName: Scalars['String'];
  options: UserOptions;
  org1?: Maybe<Scalars['String']>;
  org2?: Maybe<Scalars['String']>;
  org3?: Maybe<Scalars['String']>;
  org4?: Maybe<Scalars['String']>;
  owner?: Maybe<Owner>;
  phone?: Maybe<Scalars['String']>;
  privilegeGroups: Array<PrivilegeGroup>;
  roles: Array<Role>;
  status: VerifiedStatus;
  submissions: Array<Submission>;
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type UserLinks = {
  privilegeGroups?: InputMaybe<Array<UpdateLinks>>;
  roleNames?: InputMaybe<Array<RoleNames>>;
};

export type UserOptions = {
  __typename?: 'UserOptions';
  cookieAcceptance?: Maybe<Scalars['Boolean']>;
  lastUsedServerVersion?: Maybe<Scalars['String']>;
  optOutAll?: Maybe<Scalars['Boolean']>;
  optOutOtherContact?: Maybe<Scalars['Boolean']>;
  optOutTeamUpdates?: Maybe<Scalars['Boolean']>;
  submissionEmailOptOut?: Maybe<Scalars['Boolean']>;
};

export type UserOptionsInput = {
  cookieAcceptance?: InputMaybe<Scalars['Boolean']>;
  lastUsedServerVersion?: InputMaybe<Scalars['String']>;
  optOutAll?: InputMaybe<Scalars['Boolean']>;
  optOutOtherContact?: InputMaybe<Scalars['Boolean']>;
  optOutTeamUpdates?: InputMaybe<Scalars['Boolean']>;
  submissionEmailOptOut?: InputMaybe<Scalars['Boolean']>;
};

export type UserPage = {
  __typename?: 'UserPage';
  pageInfo: PageInfo;
  results: Array<User>;
};

/** User or opportunity's verification status */
export enum VerifiedStatus {
  Unverified = 'UNVERIFIED',
  Verified = 'VERIFIED'
}

export type GetApplicationMetaQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetApplicationMetaQuery = { __typename?: 'Query', getApplicationMeta?: { __typename?: 'ApplicationMeta', id: string, curationMeta?: any | null } | null };

export type CreateApplicationMetaMutationVariables = Exact<{
  input: ApplicationMetaInput;
}>;


export type CreateApplicationMetaMutation = { __typename?: 'Mutation', createApplicationMeta: { __typename?: 'ApplicationMeta', id: string, curationMeta?: any | null } };

export type UpdateApplicationMetaMutationVariables = Exact<{
  input: ApplicationMetaInput;
  id: Scalars['String'];
}>;


export type UpdateApplicationMetaMutation = { __typename?: 'Mutation', updateApplicationMeta: { __typename?: 'ApplicationMeta', id: string, curationMeta?: any | null } };

export type DeleteApplicationMetaMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteApplicationMetaMutation = { __typename?: 'Mutation', deleteApplicationMeta: boolean };

export type UploadAttachmentMutationVariables = Exact<{
  input: Scalars['Upload'];
  links: AttachmentLinks;
}>;


export type UploadAttachmentMutation = { __typename?: 'Mutation', addAttachment: { __typename?: 'Attachment', id: string, name: string, mimetype?: string | null, encoding?: string | null, displayName?: string | null, notes?: string | null } };

export type GetAttachmentLocationQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetAttachmentLocationQuery = { __typename?: 'Query', getAttachmentLocation: { __typename?: 'Location', location: string } };

export type DeleteAttachmentMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteAttachmentMutation = { __typename?: 'Mutation', deleteAttachment: boolean };

export type UpdateAttachmentMutationVariables = Exact<{
  input: UpdateAttachmentInput;
}>;


export type UpdateAttachmentMutation = { __typename?: 'Mutation', updateAttachment: { __typename?: 'Attachment', id: string, name: string, displayName?: string | null, notes?: string | null } };

export type AddCategoryMutationVariables = Exact<{
  name: Scalars['String'];
}>;


export type AddCategoryMutation = { __typename?: 'Mutation', createCategory: { __typename?: 'Category', id: string, name: string } };

export type GetCategoryQueryVariables = Exact<{
  id?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
}>;


export type GetCategoryQuery = { __typename?: 'Query', getCategory?: { __typename?: 'Category', id: string, name: string } | null };

export type GetCategoriesQueryVariables = Exact<{
  pageSize?: InputMaybe<Scalars['Int']>;
  searchValue: Scalars['AnyScalar'];
}>;


export type GetCategoriesQuery = { __typename?: 'Query', queryCategories: { __typename?: 'CategoryPage', results: Array<{ __typename?: 'Category', id: string, name: string }> } };

export type CreateExistingSolutionMutationVariables = Exact<{
  input: CreateExistingSolutionInput;
  links: ExistingSolutionLinks;
}>;


export type CreateExistingSolutionMutation = { __typename?: 'Mutation', createExistingSolution: { __typename?: 'ExistingSolution', id: string, source: string, title?: string | null, organization?: string | null, needsModification: boolean, createdAt: any, updatedAt?: any | null } };

export type GetExistingSolutionQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetExistingSolutionQuery = { __typename?: 'Query', getExistingSolution?: { __typename?: 'ExistingSolution', id: string, source: string, title?: string | null, organization?: string | null, needsModification: boolean, createdAt: any, updatedAt?: any | null, opportunity: { __typename?: 'Opportunity', id: string, title: string } } | null };

export type GetExistingSolutionsByOpportunityQueryVariables = Exact<{
  opportunityId: Scalars['String'];
}>;


export type GetExistingSolutionsByOpportunityQuery = { __typename?: 'Query', getExistingSolutionsByOpportunity: Array<{ __typename?: 'ExistingSolution', id: string, source: string, title?: string | null, organization?: string | null, needsModification: boolean, createdAt: any, updatedAt?: any | null }> };

export type UpdateExistingSolutionMutationVariables = Exact<{
  id: Scalars['String'];
  input: UpdateExistingSolutionInput;
}>;


export type UpdateExistingSolutionMutation = { __typename?: 'Mutation', updateExistingSolution: { __typename?: 'ExistingSolution', id: string, source: string, title?: string | null, organization?: string | null, needsModification: boolean, updatedAt?: any | null } };

export type DeleteExistingSolutionMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteExistingSolutionMutation = { __typename?: 'Mutation', deleteExistingSolution: boolean };

export type AddLinkMutationVariables = Exact<{
  input: AddLinkInput;
  links: LinkLinks;
}>;


export type AddLinkMutation = { __typename?: 'Mutation', addLink: { __typename?: 'Link', id: string, name: string, url: string } };

export type DeleteLinkMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteLinkMutation = { __typename?: 'Mutation', deleteLink: boolean };

export type UpdateLinkMutationVariables = Exact<{
  input: UpdateLinkInput;
}>;


export type UpdateLinkMutation = { __typename?: 'Mutation', updateLink: { __typename?: 'Link', id: string, name: string, url: string } };

export type FilterOpportunitiesQueryVariables = Exact<{
  searchSortInput?: InputMaybe<SearchSortInput>;
  pagingInput?: InputMaybe<PagingInput>;
  scope?: InputMaybe<Scope>;
}>;


export type FilterOpportunitiesQuery = { __typename?: 'Query', queryOpportunities: { __typename?: 'OpportunityPage', results: Array<{ __typename?: 'Opportunity', id: string, priority?: number | null, status: OpportunityStatus, function?: string | null, title: string, solutionPathway?: string | null, statusNotes?: string | null, lastCurated?: any | null, createdAt: any, campaign?: string | null, armyModernizationPriority?: string | null, echelonApplicability?: string | null, materielSolutionType?: string | null, operationalRules?: Array<string> | null, transitionInContactLineOfEffort?: string | null, isTiCLOE: boolean, capabilityArea?: Array<string> | null, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, relatedOpportunityCount?: number | null, parentOpportunityCount?: number | null, childOpportunityCount?: number | null, linkedOpportunityCount?: number | null, visibility: OpportunityVisibility, opportunityOwnerStatuses: Array<{ __typename?: 'OpportunityOwnerStatus', status: OwnershipStatus, statusSetPreviousAt?: any | null, statusSetRemovedAt?: any | null, isRemoved: boolean, owner?: { __typename?: 'Owner', organizationRole?: string | null, user: { __typename?: 'User', emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null } } | null }>, user?: { __typename?: 'User', id: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null } | null, categories: Array<{ __typename?: 'Category', id: string, name: string }>, stakeholders: Array<{ __typename?: 'Stakeholder', id: string, name?: string | null }>, curationInfo?: { __typename?: 'CurationInfo', lastCurated?: any | null, users: Array<{ __typename?: 'User', id: string, firstName: string, lastName: string }> } | null, tenant?: { __typename?: 'Tenant', id: string, name: string, label?: string | null } | null }>, pageInfo: { __typename?: 'PageInfo', hasNext: boolean, hasPrevious: boolean, lastCursor: string, lastPageSize: number, retrievedCount: number, totalCount: number } } };

export type FindOpportunitiesQueryVariables = Exact<{
  searchSortInput?: InputMaybe<SearchSortInput>;
  pagingInput?: InputMaybe<PagingInput>;
  scope?: InputMaybe<Scope>;
}>;


export type FindOpportunitiesQuery = { __typename?: 'Query', queryOpportunities: { __typename?: 'OpportunityPage', results: Array<{ __typename?: 'Opportunity', id: string, status: OpportunityStatus, title: string, ownedOpportunities: Array<{ __typename?: 'RelatedOpportunity', id: string, type: RelatedOpportunityType, target: { __typename?: 'Opportunity', id: string, title: string } }>, owningOpportunities: Array<{ __typename?: 'RelatedOpportunity', id: string, type: RelatedOpportunityType, source: { __typename?: 'Opportunity', id: string, title: string } }> }>, pageInfo: { __typename?: 'PageInfo', hasNext: boolean, hasPrevious: boolean, lastCursor: string, lastPageSize: number, retrievedCount: number, totalCount: number } } };

export type UpdateOpportunityMutationVariables = Exact<{
  id: Scalars['String'];
  links?: InputMaybe<UpdateOpportunityLinks>;
  input: UpdateOpportunityInput;
}>;


export type UpdateOpportunityMutation = { __typename?: 'Mutation', updateOpportunity: { __typename?: 'Opportunity', lastCurated?: any | null, curationInfo?: { __typename?: 'CurationInfo', lastCurated?: any | null, users: Array<{ __typename?: 'User', id: string, firstName: string, lastName: string }> } | null } };

export type GetOpportunityQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetOpportunityQuery = { __typename?: 'Query', getOpportunity?: { __typename?: 'Opportunity', id: string, createdAt: any, lastCurated?: any | null, additionalNotes?: string | null, title: string, statement: string, context: string, status: OpportunityStatus, function?: string | null, benefits?: string | null, solutionConcepts?: string | null, campaign?: string | null, armyModernizationPriority?: string | null, echelonApplicability?: string | null, operationalRules?: Array<string> | null, transitionInContactLineOfEffort?: string | null, isTiCLOE: boolean, capabilityArea?: Array<string> | null, campaignNotes?: string | null, statusNotes?: string | null, priority?: number | null, priorityNotes?: string | null, solutionPathway?: string | null, solutionPathwayDetails?: string | null, permission?: string | null, attachmentNotes?: string | null, initiatives?: string | null, endorsements?: string | null, relatedOpportunityCount?: number | null, visibility: OpportunityVisibility, DOTMLPFPPChange?: Array<string> | null, materielSolutionType?: string | null, feasibilitySummary?: string | null, existingArmyRequirement: boolean, capabilitySponsor?: string | null, armyCapabilityManager?: Array<string> | null, opportunityOwnerStatuses: Array<{ __typename?: 'OpportunityOwnerStatus', id: string, createdAt: any, status: OwnershipStatus, statusSetPreviousAt?: any | null, statusSetRemovedAt?: any | null, isRemoved: boolean, owner?: { __typename?: 'Owner', organizationRole?: string | null, user: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null } } | null }>, submissions: Array<{ __typename?: 'Submission', id: string, title: string, statement: string, context: string, benefits?: string | null, solutionConcepts?: string | null, campaign?: string | null, user?: { __typename?: 'User', id: string, altContact?: string | null, createdAt: any, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, status: VerifiedStatus, updatedAt?: any | null } | null }>, categories: Array<{ __typename?: 'Category', id: string, name: string }>, stakeholders: Array<{ __typename?: 'Stakeholder', id: string, name?: string | null, firstName?: string | null, lastName?: string | null, title?: string | null, phone?: string | null, emailAddress?: string | null, altEmailAddress?: string | null, org?: string | null, organizationRole?: string | null }>, opportunities: Array<{ __typename?: 'Opportunity', id: string, title: string }>, ownedOpportunities: Array<{ __typename?: 'RelatedOpportunity', id: string, type: RelatedOpportunityType, target: { __typename?: 'Opportunity', id: string, title: string } }>, owningOpportunities: Array<{ __typename?: 'RelatedOpportunity', id: string, type: RelatedOpportunityType, source: { __typename?: 'Opportunity', id: string, title: string } }>, user?: { __typename?: 'User', id: string, altContact?: string | null, createdAt: any, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, status: VerifiedStatus, updatedAt?: any | null } | null, attachments: Array<{ __typename?: 'Attachment', id: string, createdAt: any, updatedAt?: any | null, name: string, encoding?: string | null, mimetype?: string | null, displayName?: string | null, notes?: string | null, createdBy?: { __typename?: 'User', firstName: string, lastName: string } | null }>, links: Array<{ __typename?: 'Link', id: string, name: string, url: string, notes?: string | null, createdAt: any, createdBy?: { __typename?: 'User', firstName: string, lastName: string } | null }>, curationInfo?: { __typename?: 'CurationInfo', lastCurated?: any | null, users: Array<{ __typename?: 'User', id: string, firstName: string, lastName: string }> } | null, projects: Array<{ __typename?: 'Project', id: string, title: string }>, existingSolutions: Array<{ __typename?: 'ExistingSolution', source: string, title?: string | null, organization?: string | null, needsModification: boolean, id: string }>, requirements: Array<{ __typename?: 'Requirement', source: string, title?: string | null, poc: string, id: string }> } | null };

export type GetOpportunitySearchResultQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetOpportunitySearchResultQuery = { __typename?: 'Query', getOpportunity?: { __typename?: 'Opportunity', id: string, status: OpportunityStatus, title: string, ownedOpportunities: Array<{ __typename?: 'RelatedOpportunity', id: string, type: RelatedOpportunityType, target: { __typename?: 'Opportunity', id: string, title: string } }>, owningOpportunities: Array<{ __typename?: 'RelatedOpportunity', id: string, type: RelatedOpportunityType, source: { __typename?: 'Opportunity', id: string, title: string } }> } | null };

export type GetOpportunitiesQueryVariables = Exact<{
  searchSortInput?: InputMaybe<SearchSortInput>;
  pagingInput?: InputMaybe<PagingInput>;
  scope?: InputMaybe<Scope>;
}>;


export type GetOpportunitiesQuery = { __typename?: 'Query', queryOpportunities: { __typename?: 'OpportunityPage', results: Array<{ __typename?: 'Opportunity', id: string, createdAt: any, lastCurated?: any | null, additionalNotes?: string | null, title: string, statement: string, context: string, status: OpportunityStatus, function?: string | null, armyModernizationPriority?: string | null, echelonApplicability?: string | null, operationalRules?: Array<string> | null, transitionInContactLineOfEffort?: string | null, isTiCLOE: boolean, capabilityArea?: Array<string> | null, benefits?: string | null, solutionConcepts?: string | null, campaign?: string | null, statusNotes?: string | null, priority?: number | null, solutionPathway?: string | null, solutionPathwayDetails?: string | null, permission?: string | null, attachmentNotes?: string | null, visibility: OpportunityVisibility, opportunityOwnerStatuses: Array<{ __typename?: 'OpportunityOwnerStatus', id: string, createdAt: any, status: OwnershipStatus, statusSetPreviousAt?: any | null, statusSetRemovedAt?: any | null, isRemoved: boolean, owner?: { __typename?: 'Owner', organizationRole?: string | null, user: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null } } | null }>, user?: { __typename?: 'User', id: string, altContact?: string | null, createdAt: any, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, status: VerifiedStatus, updatedAt?: any | null } | null, existingSolutions: Array<{ __typename?: 'ExistingSolution', source: string, title?: string | null, organization?: string | null, needsModification: boolean, id: string }>, requirements: Array<{ __typename?: 'Requirement', source: string, title?: string | null, poc: string, id: string }> }> } };

export type GetCurrentUserQueryVariables = Exact<{ [key: string]: never; }>;


export type GetCurrentUserQuery = { __typename?: 'Query', getCurrentUser?: { __typename?: 'User', id: string, firstName: string, lastName: string } | null };

export type CalcOpportunitiesQueryVariables = Exact<{
  calculation: Calculation;
  searchSortInput?: InputMaybe<SearchSortInput>;
  scope?: InputMaybe<Scope>;
}>;


export type CalcOpportunitiesQuery = { __typename?: 'Query', opportunityCalculation: { __typename?: 'CalcResponse', operationResults: Array<{ __typename?: 'OperationResult', result?: any | null }> } };

export type GetOpportunitiesReportQueryVariables = Exact<{
  scope?: InputMaybe<Scope>;
  reportInput: ReportInput;
}>;


export type GetOpportunitiesReportQuery = { __typename?: 'Query', report: { __typename?: 'ReportResponse', reports: Array<{ __typename?: 'Report', name: string, label: string, data?: any | null }> } };

export type CreateOpportunityOwnerMutationVariables = Exact<{
  input: CreateOpportunityOwnerInput;
}>;


export type CreateOpportunityOwnerMutation = { __typename?: 'Mutation', createOpportunityOwner: { __typename?: 'OpportunityOwnerStatus', status: OwnershipStatus, statusSetPreviousAt?: any | null, statusSetRemovedAt?: any | null, isRemoved: boolean, owner?: { __typename?: 'Owner', organizationRole?: string | null, user: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null } } | null } };

export type QueryOpportunityOwnersQueryVariables = Exact<{
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
}>;


export type QueryOpportunityOwnersQuery = { __typename?: 'Query', queryOpportunityOwners: { __typename?: 'OpportunityOwnerPage', results: Array<{ __typename?: 'OpportunityOwnerStatus', id: string, status: OwnershipStatus, statusSetPreviousAt?: any | null, statusSetRemovedAt?: any | null, isRemoved: boolean, createdAt: any, owner?: { __typename?: 'Owner', organizationRole?: string | null, user: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null } } | null }> } };

export type GetOpportunityOwnerQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetOpportunityOwnerQuery = { __typename?: 'Query', getOpportunityOwner?: { __typename?: 'OpportunityOwnerStatus', status: OwnershipStatus, statusSetPreviousAt?: any | null, statusSetRemovedAt?: any | null, isRemoved: boolean, owner?: { __typename?: 'Owner', organizationRole?: string | null, user: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null } } | null } | null };

export type UpdateOpportunityOwnerMutationVariables = Exact<{
  id: Scalars['String'];
  input: UpdateOpportunityOwnerInput;
}>;


export type UpdateOpportunityOwnerMutation = { __typename?: 'Mutation', updateOpportunityOwner: { __typename?: 'OpportunityOwnerStatus', status: OwnershipStatus, statusSetPreviousAt?: any | null, statusSetRemovedAt?: any | null, isRemoved: boolean } };

export type GetProjectQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetProjectQuery = { __typename?: 'Query', getProject?: { __typename?: 'Project', id: string, createdAt: any, lastCurated?: any | null, title: string, summary?: string | null, status: ProjectStatus, background?: string | null, startDate?: any | null, endDate?: any | null, goals?: string | null, type?: string | null, otherType?: string | null, statusNotes?: string | null, opportunities: Array<{ __typename?: 'Opportunity', id: string, title: string, stakeholders: Array<{ __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null }> }>, categories: Array<{ __typename?: 'Category', id: string, name: string }>, projectStakeholders: Array<{ __typename?: 'ProjectStakeholder', id: string, type: ProjectStakeholderType, stakeholder: { __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null, firstName?: string | null, lastName?: string | null, emailAddress?: string | null, altEmailAddress?: string | null, phone?: string | null, title?: string | null, organizationRole?: string | null } }>, creator?: { __typename?: 'User', id: string, createdAt: any, emailAddress: string, firstName: string, lastName: string } | null, attachments: Array<{ __typename?: 'Attachment', id: string, createdAt: any, updatedAt?: any | null, name: string, encoding?: string | null, mimetype?: string | null, displayName?: string | null, notes?: string | null, createdBy?: { __typename?: 'User', firstName: string, lastName: string } | null }>, curationInfo?: { __typename?: 'CurationInfo', lastCurated?: any | null, users: Array<{ __typename?: 'User', id: string, firstName: string, lastName: string }> } | null } | null };

export type CreateProjectFromOpportunityMutationVariables = Exact<{
  input: CreateProjectFromOpportunityInput;
}>;


export type CreateProjectFromOpportunityMutation = { __typename?: 'Mutation', createProjectFromOpportunity: { __typename?: 'Project', id: string, createdAt: any, lastCurated?: any | null, title: string, summary?: string | null, status: ProjectStatus, background?: string | null, startDate?: any | null, endDate?: any | null, goals?: string | null, type?: string | null, otherType?: string | null, statusNotes?: string | null, opportunities: Array<{ __typename?: 'Opportunity', id: string, title: string }>, categories: Array<{ __typename?: 'Category', id: string, name: string }>, projectStakeholders: Array<{ __typename?: 'ProjectStakeholder', id: string, type: ProjectStakeholderType, stakeholder: { __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null } }>, creator?: { __typename?: 'User', id: string, createdAt: any, emailAddress: string, firstName: string, lastName: string } | null, attachments: Array<{ __typename?: 'Attachment', id: string, createdAt: any, updatedAt?: any | null, name: string, encoding?: string | null, mimetype?: string | null, displayName?: string | null, notes?: string | null, createdBy?: { __typename?: 'User', firstName: string, lastName: string } | null }>, curationInfo?: { __typename?: 'CurationInfo', lastCurated?: any | null, users: Array<{ __typename?: 'User', id: string, firstName: string, lastName: string }> } | null } };

export type UpdateProjectMutationVariables = Exact<{
  input: UpdateProjectInput;
  id: Scalars['String'];
  links?: InputMaybe<UpdateProjectLinks>;
}>;


export type UpdateProjectMutation = { __typename?: 'Mutation', updateProject: { __typename?: 'Project', lastCurated?: any | null, curationInfo?: { __typename?: 'CurationInfo', lastCurated?: any | null, users: Array<{ __typename?: 'User', id: string, firstName: string, lastName: string }> } | null } };

export type QueryProjectsQueryVariables = Exact<{
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
}>;


export type QueryProjectsQuery = { __typename?: 'Query', queryProjects: { __typename?: 'ProjectPage', results: Array<{ __typename?: 'Project', id: string, createdAt: any, lastCurated?: any | null, title: string, status: ProjectStatus, type?: string | null, startDate?: any | null, endDate?: any | null, otherType?: string | null, statusNotes?: string | null, projectStakeholders: Array<{ __typename?: 'ProjectStakeholder', id: string, type: ProjectStakeholderType, stakeholder: { __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null } }> }>, pageInfo: { __typename?: 'PageInfo', hasNext: boolean, hasPrevious: boolean, lastCursor: string, lastPageSize: number, retrievedCount: number, totalCount: number } } };

export type DeleteProjectMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteProjectMutation = { __typename?: 'Mutation', deleteProject: boolean };

export type CreateRequirementMutationVariables = Exact<{
  input: CreateRequirementInput;
  links: RequirementLinks;
}>;


export type CreateRequirementMutation = { __typename?: 'Mutation', createRequirement: { __typename?: 'Requirement', id: string, source: string, title?: string | null, poc: string, createdAt: any, updatedAt?: any | null } };

export type GetRequirementQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetRequirementQuery = { __typename?: 'Query', getRequirement?: { __typename?: 'Requirement', id: string, source: string, title?: string | null, poc: string, createdAt: any, updatedAt?: any | null, opportunity: { __typename?: 'Opportunity', id: string, title: string } } | null };

export type GetRequirementsByOpportunityQueryVariables = Exact<{
  opportunityId: Scalars['String'];
}>;


export type GetRequirementsByOpportunityQuery = { __typename?: 'Query', getRequirementsByOpportunity: Array<{ __typename?: 'Requirement', id: string, source: string, title?: string | null, poc: string, createdAt: any, updatedAt?: any | null }> };

export type UpdateRequirementMutationVariables = Exact<{
  id: Scalars['String'];
  input: UpdateRequirementInput;
}>;


export type UpdateRequirementMutation = { __typename?: 'Mutation', updateRequirement: { __typename?: 'Requirement', id: string, source: string, title?: string | null, poc: string, updatedAt?: any | null } };

export type DeleteRequirementMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteRequirementMutation = { __typename?: 'Mutation', deleteRequirement: boolean };

export type CreateStakeholderMutationVariables = Exact<{
  input: CreateStakeholderInput;
}>;


export type CreateStakeholderMutation = { __typename?: 'Mutation', createStakeholder: { __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null, firstName?: string | null, lastName?: string | null, title?: string | null, phone?: string | null, emailAddress?: string | null, altEmailAddress?: string | null, organizationRole?: string | null } };

export type GetStakeholderQueryVariables = Exact<{
  id?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  org?: InputMaybe<Scalars['String']>;
}>;


export type GetStakeholderQuery = { __typename?: 'Query', getStakeholder?: { __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null, firstName?: string | null, lastName?: string | null, title?: string | null, phone?: string | null, emailAddress?: string | null, altEmailAddress?: string | null, organizationRole?: string | null } | null };

export type QueryStakeholdersQueryVariables = Exact<{
  pagingInput?: InputMaybe<PagingInput>;
  searchSortInput?: InputMaybe<SearchSortInput>;
}>;


export type QueryStakeholdersQuery = { __typename?: 'Query', queryStakeholders: { __typename?: 'StakeholderPage', results: Array<{ __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null, firstName?: string | null, lastName?: string | null, title?: string | null, phone?: string | null, emailAddress?: string | null, altEmailAddress?: string | null, organizationRole?: string | null }> } };

export type UpdateStakeholderMutationVariables = Exact<{
  id: Scalars['String'];
  input: UpdateStakeholderInput;
}>;


export type UpdateStakeholderMutation = { __typename?: 'Mutation', updateStakeholder: { __typename?: 'Stakeholder', id: string, name?: string | null, org?: string | null, firstName?: string | null, lastName?: string | null, title?: string | null, phone?: string | null, emailAddress?: string | null, altEmailAddress?: string | null, organizationRole?: string | null } };

export type DeleteStakeholderMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteStakeholderMutation = { __typename?: 'Mutation', deleteStakeholder: boolean };

export type GetTenantQueryVariables = Exact<{
  id?: InputMaybe<Scalars['String']>;
  handleOrAlias?: InputMaybe<Scalars['String']>;
}>;


export type GetTenantQuery = { __typename?: 'Query', getTenant?: { __typename?: 'Tenant', id: string, name: string } | null };

export type GetTenantInfoQueryVariables = Exact<{
  handleOrAlias?: InputMaybe<Scalars['String']>;
}>;


export type GetTenantInfoQuery = { __typename?: 'Query', getTenantInfo?: { __typename?: 'TenantInfo', tenantId: string, name: string, serverVersion: string, meta?: { __typename?: 'TenantMeta', config?: any | null, theme?: any | null, content?: any | null, filterOtherPrivateOpportunities: boolean } | null } | null };

export type QueryTenantsQueryVariables = Exact<{
  searchSortInput?: InputMaybe<SearchSortInput>;
  pagingInput?: InputMaybe<PagingInput>;
}>;


export type QueryTenantsQuery = { __typename?: 'Query', queryTenants: { __typename?: 'TenantPage', results: Array<{ __typename?: 'Tenant', id: string, name: string, meta?: { __typename?: 'TenantMeta', config?: any | null, content?: any | null, filterOtherPrivateOpportunities: boolean } | null }> } };

export type RegisterUserMutationVariables = Exact<{
  input: RegisterUserInput;
}>;


export type RegisterUserMutation = { __typename?: 'Mutation', register: { __typename?: 'AuthResponse', token: string, user: { __typename?: 'User', id: string } } };

export type UserLoginMutationVariables = Exact<{
  tenantHandle: Scalars['String'];
  password: Scalars['String'];
  userName: Scalars['String'];
}>;


export type UserLoginMutation = { __typename?: 'Mutation', login: { __typename?: 'AuthResponse', token: string, expiresAt: any, user: { __typename?: 'User', id: string, emailAddress: string, status: VerifiedStatus, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null, roles: Array<{ __typename?: 'Role', id: string, name: RoleNames }>, appMeta?: { __typename?: 'ApplicationMeta', id: string, curationMeta?: any | null } | null, options: { __typename?: 'UserOptions', lastUsedServerVersion?: string | null }, privilegeGroups: Array<{ __typename?: 'PrivilegeGroup', id: string, name: string, privileges?: Array<{ __typename?: 'Privilege', id: string, name: string, resourceId?: string | null, resourceType: ResourceType }> | null }> } } };

export type RenewTokenMutationVariables = Exact<{ [key: string]: never; }>;


export type RenewTokenMutation = { __typename?: 'Mutation', renew: { __typename?: 'AuthResponse', token: string, expiresAt: any } };

export type UpdateCurrentUserMutationVariables = Exact<{
  input: UpdateCurrentUserInput;
  links?: InputMaybe<UpdateCurrentUserLinks>;
}>;


export type UpdateCurrentUserMutation = { __typename?: 'Mutation', updateCurrentUser: { __typename?: 'User', id: string } };

export type DeleteCurrentUserMutationVariables = Exact<{ [key: string]: never; }>;


export type DeleteCurrentUserMutation = { __typename?: 'Mutation', deleteCurrentUser: boolean };

export type GetCurrentUserStatusQueryVariables = Exact<{ [key: string]: never; }>;


export type GetCurrentUserStatusQuery = { __typename?: 'Query', getCurrentUser?: { __typename?: 'User', id: string, status: VerifiedStatus } | null };

export type GetCurrentUserInformationQueryVariables = Exact<{ [key: string]: never; }>;


export type GetCurrentUserInformationQuery = { __typename?: 'Query', getCurrentUser?: { __typename?: 'User', id: string, emailAddress: string, status: VerifiedStatus, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null, roles: Array<{ __typename?: 'Role', id: string, name: RoleNames }>, appMeta?: { __typename?: 'ApplicationMeta', id: string, curationMeta?: any | null } | null, options: { __typename?: 'UserOptions', lastUsedServerVersion?: string | null }, privilegeGroups: Array<{ __typename?: 'PrivilegeGroup', id: string, name: string, privileges?: Array<{ __typename?: 'Privilege', id: string, name: string, resourceId?: string | null, resourceType: ResourceType }> | null }> } | null };

export type QueryUsersQueryVariables = Exact<{
  searchSortInput?: InputMaybe<SearchSortInput>;
  pagingInput?: InputMaybe<PagingInput>;
}>;


export type QueryUsersQuery = { __typename?: 'Query', queryUsers: { __typename?: 'UserPage', results: Array<{ __typename?: 'User', id: string, emailAddress: string, status: VerifiedStatus, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, altContact?: string | null, roles: Array<{ __typename?: 'Role', id: string, name: RoleNames }>, owner?: { __typename?: 'Owner', organizationRole?: string | null } | null }>, pageInfo: { __typename?: 'PageInfo', hasNext: boolean, hasPrevious: boolean, lastCursor: string, lastPageSize: number, retrievedCount: number, totalCount: number } } };

export type DeleteUserMutationVariables = Exact<{
  id: Scalars['String'];
}>;


export type DeleteUserMutation = { __typename?: 'Mutation', deleteUser: boolean };

export type GetUserQueryVariables = Exact<{
  id: Scalars['String'];
}>;


export type GetUserQuery = { __typename?: 'Query', getUser?: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, phone?: string | null, status: VerifiedStatus, roles: Array<{ __typename?: 'Role', id: string, name: RoleNames }> } | null };

export type CreateUserMutationVariables = Exact<{
  input: CreateUserInput;
  links?: InputMaybe<UserLinks>;
}>;


export type CreateUserMutation = { __typename?: 'Mutation', createUser: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, status: VerifiedStatus, phone?: string | null, roles: Array<{ __typename?: 'Role', id: string, name: RoleNames }> } };

export type UpdateUserMutationVariables = Exact<{
  input: UpdateUserInput;
  id: Scalars['String'];
  links?: InputMaybe<UserLinks>;
}>;


export type UpdateUserMutation = { __typename?: 'Mutation', updateUser: { __typename?: 'User', id: string, emailAddress: string, firstName: string, lastName: string, org1?: string | null, org2?: string | null, org3?: string | null, org4?: string | null, status: VerifiedStatus, phone?: string | null, roles: Array<{ __typename?: 'Role', id: string, name: RoleNames }> } };


export const GetApplicationMetaDocument = gql`
    query getApplicationMeta($id: String!) {
  getApplicationMeta(id: $id) {
    id
    curationMeta
  }
}
    `;
export const CreateApplicationMetaDocument = gql`
    mutation createApplicationMeta($input: ApplicationMetaInput!) {
  createApplicationMeta(input: $input) {
    id
    curationMeta
  }
}
    `;
export const UpdateApplicationMetaDocument = gql`
    mutation updateApplicationMeta($input: ApplicationMetaInput!, $id: String!) {
  updateApplicationMeta(input: $input, id: $id) {
    id
    curationMeta
  }
}
    `;
export const DeleteApplicationMetaDocument = gql`
    mutation deleteApplicationMeta($id: String!) {
  deleteApplicationMeta(id: $id)
}
    `;
export const UploadAttachmentDocument = gql`
    mutation uploadAttachment($input: Upload!, $links: AttachmentLinks!) {
  addAttachment(input: $input, links: $links) {
    id
    name
    mimetype
    encoding
    displayName
    notes
  }
}
    `;
export const GetAttachmentLocationDocument = gql`
    query getAttachmentLocation($id: String!) {
  getAttachmentLocation(id: $id) {
    location
  }
}
    `;
export const DeleteAttachmentDocument = gql`
    mutation deleteAttachment($id: String!) {
  deleteAttachment(id: $id)
}
    `;
export const UpdateAttachmentDocument = gql`
    mutation updateAttachment($input: UpdateAttachmentInput!) {
  updateAttachment(input: $input) {
    id
    name
    displayName
    notes
  }
}
    `;
export const AddCategoryDocument = gql`
    mutation addCategory($name: String!) {
  createCategory(input: {name: $name}) {
    id
    name
  }
}
    `;
export const GetCategoryDocument = gql`
    query getCategory($id: String, $name: String) {
  getCategory(id: $id, name: $name) {
    id
    name
  }
}
    `;
export const GetCategoriesDocument = gql`
    query getCategories($pageSize: Int = 10, $searchValue: AnyScalar!) {
  queryCategories(
    pagingInput: {pageSize: $pageSize}
    searchSortInput: {sortFields: [{fieldName: "name", ascending: true}], searchFields: [{fieldNames: ["name"], operator: MATCH, searchValue: $searchValue}]}
  ) {
    results {
      id
      name
    }
  }
}
    `;
export const CreateExistingSolutionDocument = gql`
    mutation createExistingSolution($input: CreateExistingSolutionInput!, $links: ExistingSolutionLinks!) {
  createExistingSolution(input: $input, links: $links) {
    id
    source
    title
    organization
    needsModification
    createdAt
    updatedAt
  }
}
    `;
export const GetExistingSolutionDocument = gql`
    query getExistingSolution($id: String!) {
  getExistingSolution(id: $id) {
    id
    source
    title
    organization
    needsModification
    createdAt
    updatedAt
    opportunity {
      id
      title
    }
  }
}
    `;
export const GetExistingSolutionsByOpportunityDocument = gql`
    query getExistingSolutionsByOpportunity($opportunityId: String!) {
  getExistingSolutionsByOpportunity(opportunityId: $opportunityId) {
    id
    source
    title
    organization
    needsModification
    createdAt
    updatedAt
  }
}
    `;
export const UpdateExistingSolutionDocument = gql`
    mutation updateExistingSolution($id: String!, $input: UpdateExistingSolutionInput!) {
  updateExistingSolution(id: $id, input: $input) {
    id
    source
    title
    organization
    needsModification
    updatedAt
  }
}
    `;
export const DeleteExistingSolutionDocument = gql`
    mutation deleteExistingSolution($id: String!) {
  deleteExistingSolution(id: $id)
}
    `;
export const AddLinkDocument = gql`
    mutation addLink($input: AddLinkInput!, $links: LinkLinks!) {
  addLink(input: $input, links: $links) {
    id
    name
    url
  }
}
    `;
export const DeleteLinkDocument = gql`
    mutation deleteLink($id: String!) {
  deleteLink(id: $id)
}
    `;
export const UpdateLinkDocument = gql`
    mutation updateLink($input: UpdateLinkInput!) {
  updateLink(input: $input) {
    id
    name
    url
  }
}
    `;
export const FilterOpportunitiesDocument = gql`
    query filterOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(
    searchSortInput: $searchSortInput
    pagingInput: $pagingInput
    scope: $scope
  ) {
    results {
      id
      priority
      status
      function
      title
      solutionPathway
      statusNotes
      lastCurated
      createdAt
      campaign
      armyModernizationPriority
      echelonApplicability
      materielSolutionType
      opportunityOwnerStatuses {
        status
        statusSetPreviousAt
        statusSetRemovedAt
        isRemoved
        owner {
          organizationRole
          user {
            emailAddress
            firstName
            lastName
            org1
            org2
            org3
            org4
            phone
            altContact
          }
        }
      }
      operationalRules
      transitionInContactLineOfEffort
      isTiCLOE
      capabilityArea
      org1
      org2
      org3
      org4
      relatedOpportunityCount
      parentOpportunityCount
      childOpportunityCount
      linkedOpportunityCount
      visibility
      user {
        id
        org1
        org2
        org3
        org4
      }
      categories {
        id
        name
      }
      stakeholders {
        id
        name
      }
      curationInfo {
        users {
          id
          firstName
          lastName
        }
        lastCurated
      }
      tenant {
        id
        name
        label
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}
    `;
export const FindOpportunitiesDocument = gql`
    query findOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(
    searchSortInput: $searchSortInput
    pagingInput: $pagingInput
    scope: $scope
  ) {
    results {
      id
      status
      title
      ownedOpportunities {
        id
        type
        target {
          id
          title
        }
      }
      owningOpportunities {
        id
        type
        source {
          id
          title
        }
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}
    `;
export const UpdateOpportunityDocument = gql`
    mutation updateOpportunity($id: String!, $links: UpdateOpportunityLinks, $input: UpdateOpportunityInput!) {
  updateOpportunity(links: $links, id: $id, input: $input) {
    lastCurated
    curationInfo {
      lastCurated
      users {
        id
        firstName
        lastName
      }
    }
  }
}
    `;
export const GetOpportunityDocument = gql`
    query getOpportunity($id: String!) {
  getOpportunity(id: $id) {
    id
    createdAt
    lastCurated
    additionalNotes
    title
    statement
    context
    status
    function
    benefits
    solutionConcepts
    campaign
    armyModernizationPriority
    echelonApplicability
    operationalRules
    transitionInContactLineOfEffort
    isTiCLOE
    capabilityArea
    campaignNotes
    statusNotes
    priority
    priorityNotes
    solutionPathway
    solutionPathwayDetails
    permission
    attachmentNotes
    initiatives
    endorsements
    relatedOpportunityCount
    visibility
    DOTMLPFPPChange
    materielSolutionType
    feasibilitySummary
    existingArmyRequirement
    capabilitySponsor
    armyCapabilityManager
    opportunityOwnerStatuses {
      id
      createdAt
      status
      statusSetPreviousAt
      statusSetRemovedAt
      isRemoved
      owner {
        organizationRole
        user {
          id
          emailAddress
          firstName
          lastName
          org1
          org2
          org3
          org4
          phone
          altContact
        }
      }
    }
    submissions {
      id
      title
      statement
      context
      benefits
      solutionConcepts
      campaign
      user {
        id
        altContact
        createdAt
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        status
        updatedAt
      }
    }
    categories {
      id
      name
    }
    stakeholders {
      id
      name
      firstName
      lastName
      title
      phone
      emailAddress
      altEmailAddress
      org
      organizationRole
    }
    opportunities {
      id
      title
    }
    ownedOpportunities {
      id
      type
      target {
        id
        title
      }
    }
    owningOpportunities {
      id
      type
      source {
        id
        title
      }
    }
    user {
      id
      altContact
      createdAt
      emailAddress
      firstName
      lastName
      org1
      org2
      org3
      org4
      phone
      status
      updatedAt
    }
    attachments {
      id
      createdAt
      updatedAt
      name
      encoding
      mimetype
      displayName
      createdBy {
        firstName
        lastName
      }
      notes
    }
    links {
      id
      name
      url
      notes
      createdAt
      createdBy {
        firstName
        lastName
      }
    }
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
    projects {
      id
      title
    }
    existingSolutions {
      source
      title
      organization
      needsModification
      id
    }
    requirements {
      source
      title
      poc
      id
    }
  }
}
    `;
export const GetOpportunitySearchResultDocument = gql`
    query getOpportunitySearchResult($id: String!) {
  getOpportunity(id: $id) {
    id
    status
    title
    ownedOpportunities {
      id
      type
      target {
        id
        title
      }
    }
    owningOpportunities {
      id
      type
      source {
        id
        title
      }
    }
  }
}
    `;
export const GetOpportunitiesDocument = gql`
    query getOpportunities($searchSortInput: SearchSortInput, $pagingInput: PagingInput, $scope: Scope) {
  queryOpportunities(
    searchSortInput: $searchSortInput
    pagingInput: $pagingInput
    scope: $scope
  ) {
    results {
      id
      createdAt
      lastCurated
      additionalNotes
      title
      statement
      context
      status
      function
      armyModernizationPriority
      echelonApplicability
      opportunityOwnerStatuses {
        id
        createdAt
        status
        statusSetPreviousAt
        statusSetRemovedAt
        isRemoved
        owner {
          organizationRole
          user {
            id
            emailAddress
            firstName
            lastName
            org1
            org2
            org3
            org4
            phone
            altContact
          }
        }
      }
      operationalRules
      transitionInContactLineOfEffort
      isTiCLOE
      capabilityArea
      benefits
      solutionConcepts
      campaign
      statusNotes
      priority
      solutionPathway
      solutionPathwayDetails
      permission
      attachmentNotes
      visibility
      user {
        id
        altContact
        createdAt
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        status
        updatedAt
      }
      existingSolutions {
        source
        title
        organization
        needsModification
        id
      }
      requirements {
        source
        title
        poc
        id
      }
    }
  }
}
    `;
export const GetCurrentUserDocument = gql`
    query getCurrentUser {
  getCurrentUser {
    id
    firstName
    lastName
  }
}
    `;
export const CalcOpportunitiesDocument = gql`
    query calcOpportunities($calculation: Calculation!, $searchSortInput: SearchSortInput, $scope: Scope) {
  opportunityCalculation(
    calculation: $calculation
    searchSortInput: $searchSortInput
    scope: $scope
  ) {
    operationResults {
      result
    }
  }
}
    `;
export const GetOpportunitiesReportDocument = gql`
    query getOpportunitiesReport($scope: Scope, $reportInput: ReportInput!) {
  report(scope: $scope, reportInput: $reportInput) {
    reports {
      name
      label
      data
    }
  }
}
    `;
export const CreateOpportunityOwnerDocument = gql`
    mutation createOpportunityOwner($input: CreateOpportunityOwnerInput!) {
  createOpportunityOwner(input: $input) {
    status
    statusSetPreviousAt
    statusSetRemovedAt
    isRemoved
    owner {
      organizationRole
      user {
        id
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        altContact
      }
    }
  }
}
    `;
export const QueryOpportunityOwnersDocument = gql`
    query queryOpportunityOwners($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryOpportunityOwners(
    pagingInput: $pagingInput
    searchSortInput: $searchSortInput
  ) {
    results {
      id
      status
      statusSetPreviousAt
      statusSetRemovedAt
      isRemoved
      createdAt
      owner {
        organizationRole
        user {
          id
          emailAddress
          firstName
          lastName
          org1
          org2
          org3
          org4
          phone
          altContact
        }
      }
    }
  }
}
    `;
export const GetOpportunityOwnerDocument = gql`
    query getOpportunityOwner($id: String!) {
  getOpportunityOwner(id: $id) {
    status
    statusSetPreviousAt
    statusSetRemovedAt
    isRemoved
    owner {
      organizationRole
      user {
        id
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        altContact
      }
    }
  }
}
    `;
export const UpdateOpportunityOwnerDocument = gql`
    mutation updateOpportunityOwner($id: String!, $input: UpdateOpportunityOwnerInput!) {
  updateOpportunityOwner(id: $id, input: $input) {
    status
    statusSetPreviousAt
    statusSetRemovedAt
    isRemoved
  }
}
    `;
export const GetProjectDocument = gql`
    query getProject($id: String!) {
  getProject(id: $id) {
    id
    createdAt
    lastCurated
    title
    summary
    status
    background
    startDate
    endDate
    goals
    type
    otherType
    statusNotes
    opportunities {
      id
      title
      stakeholders {
        id
        name
        org
      }
    }
    categories {
      id
      name
    }
    projectStakeholders {
      id
      type
      stakeholder {
        id
        name
        org
        firstName
        lastName
        emailAddress
        altEmailAddress
        phone
        title
        organizationRole
      }
    }
    creator {
      id
      createdAt
      emailAddress
      firstName
      lastName
    }
    attachments {
      id
      createdAt
      updatedAt
      name
      encoding
      mimetype
      displayName
      notes
      createdBy {
        firstName
        lastName
      }
    }
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
  }
}
    `;
export const CreateProjectFromOpportunityDocument = gql`
    mutation createProjectFromOpportunity($input: CreateProjectFromOpportunityInput!) {
  createProjectFromOpportunity(input: $input) {
    id
    createdAt
    lastCurated
    title
    summary
    status
    background
    startDate
    endDate
    goals
    type
    otherType
    statusNotes
    opportunities {
      id
      title
    }
    categories {
      id
      name
    }
    projectStakeholders {
      id
      type
      stakeholder {
        id
        name
        org
      }
    }
    creator {
      id
      createdAt
      emailAddress
      firstName
      lastName
    }
    attachments {
      id
      createdAt
      updatedAt
      name
      encoding
      mimetype
      displayName
      notes
      createdBy {
        firstName
        lastName
      }
    }
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
  }
}
    `;
export const UpdateProjectDocument = gql`
    mutation updateProject($input: UpdateProjectInput!, $id: String!, $links: UpdateProjectLinks) {
  updateProject(input: $input, links: $links, id: $id) {
    lastCurated
    curationInfo {
      users {
        id
        firstName
        lastName
      }
      lastCurated
    }
  }
}
    `;
export const QueryProjectsDocument = gql`
    query queryProjects($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryProjects(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      createdAt
      lastCurated
      title
      status
      type
      startDate
      endDate
      otherType
      statusNotes
      projectStakeholders {
        id
        type
        stakeholder {
          id
          name
          org
        }
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}
    `;
export const DeleteProjectDocument = gql`
    mutation deleteProject($id: String!) {
  deleteProject(id: $id)
}
    `;
export const CreateRequirementDocument = gql`
    mutation createRequirement($input: CreateRequirementInput!, $links: RequirementLinks!) {
  createRequirement(input: $input, links: $links) {
    id
    source
    title
    poc
    createdAt
    updatedAt
  }
}
    `;
export const GetRequirementDocument = gql`
    query getRequirement($id: String!) {
  getRequirement(id: $id) {
    id
    source
    title
    poc
    createdAt
    updatedAt
    opportunity {
      id
      title
    }
  }
}
    `;
export const GetRequirementsByOpportunityDocument = gql`
    query getRequirementsByOpportunity($opportunityId: String!) {
  getRequirementsByOpportunity(opportunityId: $opportunityId) {
    id
    source
    title
    poc
    createdAt
    updatedAt
  }
}
    `;
export const UpdateRequirementDocument = gql`
    mutation updateRequirement($id: String!, $input: UpdateRequirementInput!) {
  updateRequirement(id: $id, input: $input) {
    id
    source
    title
    poc
    updatedAt
  }
}
    `;
export const DeleteRequirementDocument = gql`
    mutation deleteRequirement($id: String!) {
  deleteRequirement(id: $id)
}
    `;
export const CreateStakeholderDocument = gql`
    mutation createStakeholder($input: CreateStakeholderInput!) {
  createStakeholder(input: $input) {
    id
    name
    org
    firstName
    lastName
    title
    phone
    emailAddress
    altEmailAddress
    organizationRole
  }
}
    `;
export const GetStakeholderDocument = gql`
    query getStakeholder($id: String, $name: String, $org: String) {
  getStakeholder(name: $name, id: $id, org: $org) {
    id
    name
    org
    firstName
    lastName
    title
    phone
    emailAddress
    altEmailAddress
    organizationRole
  }
}
    `;
export const QueryStakeholdersDocument = gql`
    query queryStakeholders($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryStakeholders(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      name
      org
      firstName
      lastName
      title
      phone
      emailAddress
      altEmailAddress
      organizationRole
    }
  }
}
    `;
export const UpdateStakeholderDocument = gql`
    mutation updateStakeholder($id: String!, $input: UpdateStakeholderInput!) {
  updateStakeholder(id: $id, input: $input) {
    id
    name
    org
    firstName
    lastName
    title
    phone
    emailAddress
    altEmailAddress
    organizationRole
  }
}
    `;
export const DeleteStakeholderDocument = gql`
    mutation deleteStakeholder($id: String!) {
  deleteStakeholder(id: $id)
}
    `;
export const GetTenantDocument = gql`
    query getTenant($id: String, $handleOrAlias: String) {
  getTenant(id: $id, handleOrAlias: $handleOrAlias) {
    id
    name
  }
}
    `;
export const GetTenantInfoDocument = gql`
    query getTenantInfo($handleOrAlias: String) {
  getTenantInfo(handleOrAlias: $handleOrAlias) {
    tenantId
    name
    serverVersion
    meta {
      config
      theme
      content
      filterOtherPrivateOpportunities
    }
  }
}
    `;
export const QueryTenantsDocument = gql`
    query queryTenants($searchSortInput: SearchSortInput, $pagingInput: PagingInput) {
  queryTenants(searchSortInput: $searchSortInput, pagingInput: $pagingInput) {
    results {
      id
      name
      meta {
        config
        content
        filterOtherPrivateOpportunities
      }
    }
  }
}
    `;
export const RegisterUserDocument = gql`
    mutation registerUser($input: RegisterUserInput!) {
  register(input: $input) {
    user {
      id
    }
    token
  }
}
    `;
export const UserLoginDocument = gql`
    mutation userLogin($tenantHandle: String!, $password: String!, $userName: String!) {
  login(tenantHandle: $tenantHandle, password: $password, userName: $userName) {
    user {
      id
      emailAddress
      status
      firstName
      lastName
      org1
      org2
      org3
      org4
      phone
      altContact
      roles {
        id
        name
      }
      appMeta {
        id
        curationMeta
      }
      options {
        lastUsedServerVersion
      }
      privilegeGroups {
        id
        name
        privileges {
          id
          name
          resourceId
          resourceType
        }
      }
    }
    token
    expiresAt
  }
}
    `;
export const RenewTokenDocument = gql`
    mutation renewToken {
  renew {
    token
    expiresAt
  }
}
    `;
export const UpdateCurrentUserDocument = gql`
    mutation updateCurrentUser($input: UpdateCurrentUserInput!, $links: UpdateCurrentUserLinks) {
  updateCurrentUser(input: $input, links: $links) {
    id
  }
}
    `;
export const DeleteCurrentUserDocument = gql`
    mutation deleteCurrentUser {
  deleteCurrentUser
}
    `;
export const GetCurrentUserStatusDocument = gql`
    query getCurrentUserStatus {
  getCurrentUser {
    id
    status
  }
}
    `;
export const GetCurrentUserInformationDocument = gql`
    query getCurrentUserInformation {
  getCurrentUser {
    id
    emailAddress
    status
    firstName
    lastName
    org1
    org2
    org3
    org4
    phone
    altContact
    roles {
      id
      name
    }
    appMeta {
      id
      curationMeta
    }
    options {
      lastUsedServerVersion
    }
    privilegeGroups {
      id
      name
      privileges {
        id
        name
        resourceId
        resourceType
      }
    }
  }
}
    `;
export const QueryUsersDocument = gql`
    query queryUsers($searchSortInput: SearchSortInput, $pagingInput: PagingInput) {
  queryUsers(searchSortInput: $searchSortInput, pagingInput: $pagingInput) {
    results {
      id
      emailAddress
      status
      firstName
      lastName
      org1
      org2
      org3
      org4
      phone
      altContact
      roles {
        id
        name
      }
      owner {
        organizationRole
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}
    `;
export const DeleteUserDocument = gql`
    mutation deleteUser($id: String!) {
  deleteUser(id: $id)
}
    `;
export const GetUserDocument = gql`
    query getUser($id: String!) {
  getUser(id: $id) {
    id
    emailAddress
    firstName
    lastName
    org1
    org2
    org3
    org4
    phone
    status
    roles {
      id
      name
    }
  }
}
    `;
export const CreateUserDocument = gql`
    mutation createUser($input: CreateUserInput!, $links: UserLinks) {
  createUser(input: $input, links: $links) {
    id
    emailAddress
    firstName
    lastName
    org1
    org2
    org3
    org4
    status
    phone
    roles {
      id
      name
    }
  }
}
    `;
export const UpdateUserDocument = gql`
    mutation updateUser($input: UpdateUserInput!, $id: String!, $links: UserLinks) {
  updateUser(input: $input, id: $id, links: $links) {
    id
    emailAddress
    firstName
    lastName
    org1
    org2
    org3
    org4
    status
    phone
    roles {
      id
      name
    }
  }
}
    `;